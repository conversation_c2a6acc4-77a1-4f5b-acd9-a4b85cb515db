{"database": "dgr_db_prod", "tables": [{"name": "dgr_solar_db", "description": "Daily Generation Report for solar plants (one row per plant per day).", "primary_key": ["id"], "natural_keys": ["date", "plant_short_name"], "columns": [{"name": "id", "type": "INTEGER", "description": "Autoincrement primary key"}, {"name": "date", "type": "DATE", "description": "Date of report (YYYY-MM-DD)"}, {"name": "plant_short_name", "type": "TEXT", "description": "Short plant code (e.g. 'IN.INTE.SOL01')"}, {"name": "plant_long_name", "type": "TEXT", "description": "Human-readable plant name"}, {"name": "generation", "type": "FLOAT", "description": "Daily generation", "unit": "kWh"}, {"name": "pr", "type": "FLOAT", "description": "Daily performance ratio", "unit": "%"}, {"name": "poa", "type": "FLOAT", "description": "Daily plane of array irradiance", "unit": "kWh/m²"}, {"name": "generation_monthly", "type": "FLOAT", "description": "Month-to-date generation", "unit": "kWh"}, {"name": "pr_monthly", "type": "FLOAT", "description": "Month-to-date PR", "unit": "%"}, {"name": "poa_monthly", "type": "FLOAT", "description": "Month-to-date POA", "unit": "kWh/m²"}, {"name": "edit_generation", "type": "FLOAT", "description": "Edited daily generation", "unit": "kWh"}, {"name": "edit_pr", "type": "FLOAT", "description": "Edited daily PR", "unit": "%"}, {"name": "edit_poa", "type": "FLOAT", "description": "Edited daily POA", "unit": "kWh/m²"}, {"name": "edit_generation_monthly", "type": "FLOAT", "description": "Edited month-to-date generation", "unit": "kWh"}, {"name": "edit_pr_monthly", "type": "FLOAT", "description": "Edited month-to-date PR", "unit": "%"}, {"name": "edit_poa_monthly", "type": "FLOAT", "description": "Edited month-to-date POA", "unit": "kWh/m²"}, {"name": "approved", "type": "BOOLEAN", "description": "Approved flag"}, {"name": "review", "type": "BOOLEAN", "description": "In review flag"}, {"name": "action_performed", "type": "BOOLEAN", "description": "Any action was performed"}, {"name": "status", "type": "TEXT", "description": "Workflow status: <PERSON><PERSON>, <PERSON>ding, In Review, Not Sent, Regenerate"}, {"name": "regenerate", "type": "BOOLEAN", "description": "Marked for regeneration"}, {"name": "dgr_path", "type": "TEXT", "description": "Filesystem or S3 path to DGR file"}, {"name": "comments", "type": "TEXT", "description": "Reviewer/editor comments"}, {"name": "dont_send", "type": "BOOLEAN", "description": "Do not send to customer"}, {"name": "edit_action", "type": "BOOLEAN", "description": "An edit action occurred"}, {"name": "save_action", "type": "BOOLEAN", "description": "A save action occurred"}, {"name": "saved_count", "type": "INTEGER", "description": "How many times the report was saved"}], "access": {"allowed_select": true, "allowed_update": false, "allowed_delete": false, "allowed_insert": false}}], "units": {"generation": "kWh", "pr": "%", "poa": "kWh/m²", "generation_monthly": "kWh", "pr_monthly": "%", "poa_monthly": "kWh/m²", "edit_generation": "kWh", "edit_pr": "%", "edit_poa": "kWh/m²"}}