# import asyncio
# import sys
# import os
# from contextlib import AsyncExitStack
# from dotenv import load_dotenv

# from mcp import ClientSession, StdioServerParameters
# from mcp.client.stdio import stdio_client
# # from langchain.chat_models import Chat<PERSON><PERSON><PERSON><PERSON>
# from langchain_openai import Chat<PERSON><PERSON><PERSON>I
# from langchain_mcp_adapters.tools import load_mcp_tools
# from langchain.agents import initialize_agent, AgentType

# # Load environment variables
# load_dotenv()
# api_key = os.getenv("OPENAI_API_KEY")
# if not api_key:
#     raise ValueError("OPENAI_API_KEY not found in environment variables.")

# class MCPLangChainClient:
#     def __init__(self, server_script: str):
#         self.server_script = server_script
#         self.session = None
#         self.exit_stack = AsyncExitStack()
#         self.tools = []
#         self.agent = None

#     async def connect(self):
#         """Launch MCP server via stdio and load its tools into LangChain."""
#         interpreter = "python" if self.server_script.endswith('.py') else "node"
#         params = StdioServerParameters(
#             command=interpreter,
#             args=[self.server_script]
#         )
#         # Start stdio transport
#         read, write = await self.exit_stack.enter_async_context(
#             stdio_client(params)
#         )
#         # Create MCP session and initialize
#         self.session = await self.exit_stack.enter_async_context(
#             ClientSession(read, write)
#         )
#         await self.session.initialize()

#         # Load MCP tools for LangChain
#         self.tools = await load_mcp_tools(self.session)
#         tool_names = [t.name for t in self.tools]
#         print(f"Connected to MCP server with tools: {tool_names}")

#         # Instantiate LangChain agent
#         llm = ChatOpenAI(model_name="gpt-4o", temperature=0)
#         self.agent = initialize_agent(
#             tools=self.tools,
#             llm=llm,
#             agent=AgentType.OPENAI_FUNCTIONS,
#             verbose=True
#         )

#     async def run(self):
#         """Interactive chat loop that uses the LangChain agent to call MCP tools."""
#         print("\nLangChain MCP Agent Ready. Type 'quit' to exit.")
#         while True:
#             query = input("Query> ").strip()
#             if query.lower() in ('quit', 'exit'):
#                 break
#             try:
#                 # Let the agent handle function calls automatically
#                 # response = await self.agent.arun(query)
#                 # After
#                 response = await self.agent.ainvoke(query)
            
#                 print(f"\n{response["output"]}\n")
#             except Exception as e:
#                 print(f"Error: {e}")

#     async def close(self):
#         await self.exit_stack.aclose()

# async def main():
#     if len(sys.argv) != 2:
#         print("Usage: python client.py /path/to/mcp_server.py")
#         sys.exit(1)

#     client = MCPLangChainClient(server_script=sys.argv[1])
#     try:
#         await client.connect()
#         await client.run()
#     finally:
#         await client.close()

# if __name__ == '__main__':
#     asyncio.run(main())

# # python client.py server.py



# import asyncio
# import os
# import sys
# import uuid
# import json
# from contextlib import AsyncExitStack
# from dataclasses import dataclass
# from typing import List, Optional
# from datetime import datetime

# from dotenv import load_dotenv

# # --- MCP core (Python SDK) ---
# from mcp import ClientSession, StdioServerParameters
# from mcp.client.stdio import stdio_client

# # --- LangChain / LangGraph stack ---
# from langchain_openai import ChatOpenAI
# from langchain_mcp_adapters.tools import load_mcp_tools
# from langgraph.prebuilt import create_react_agent
# from langchain.schema import BaseMessage


# # ----------------------------
# # Config & Environment
# # ----------------------------
# load_dotenv()
# OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
# if not OPENAI_API_KEY:
#     raise ValueError("OPENAI_API_KEY not found in environment variables.")

# DEFAULT_MODEL = os.getenv("OPENAI_MODEL", "gpt-4o")
# DEFAULT_MEMORY_FILE = os.getenv("MCP_CLIENT_MEMORY_FILE", "mcp_client_memory.json")


# @dataclass
# class MCPServerSpec:
#     """Defines an MCP server for stdio connection."""
#     path: str
#     args: Optional[List[str]] = None

#     @property
#     def command(self) -> str:
#         return "python" if self.path.endswith(".py") else "node"

#     @property
#     def full_args(self) -> List[str]:
#         return [self.path] + (self.args or [])


# class MCPMultiServerClient:
#     def __init__(self, servers: List[MCPServerSpec]):
#         print("DEBUG: Initializing MCPMultiServerClient with servers:", servers)
#         self.servers = servers
#         self.exit_stack = AsyncExitStack()
#         self.sessions: List[ClientSession] = []
#         self.tools = []
#         self.app = None

#         # Setup memory file
#         self.memory_file = DEFAULT_MEMORY_FILE
#         print(f"DEBUG: Using memory file: {self.memory_file}")
#         os.makedirs(os.path.dirname(self.memory_file) or ".", exist_ok=True)
#         if os.path.exists(self.memory_file):
#             try:
#                 with open(self.memory_file, "r", encoding="utf-8") as f:
#                     self.memory = json.load(f)
#                 print("DEBUG: Loaded memory from file.")
#             except Exception as e:
#                 print(f"DEBUG: Failed to load memory file: {e}")
#                 self.memory = {}
#         else:
#             print("DEBUG: Memory file does not exist, starting fresh.")
#             self.memory = {}

#         # Ensure thread entry exists
#         self.thread_id = self.memory.get("thread_id", str(uuid.uuid4()))
#         print(f"DEBUG: Using thread_id: {self.thread_id}")
#         self.memory.setdefault(self.thread_id, [])
#         self.memory["thread_id"] = self.thread_id
#         self.save_memory()

#     def save_memory(self):
#         print("DEBUG: Saving memory to file.")
#         with open(self.memory_file, "w", encoding="utf-8") as f:
#             json.dump(self.memory, f, indent=2)

#     async def _connect_one(self, spec: MCPServerSpec) -> ClientSession:
#         print(f"DEBUG: Connecting to MCP server: {spec.path} with args: {spec.full_args}")
#         params = StdioServerParameters(command=spec.command, args=spec.full_args)
#         read, write = await self.exit_stack.enter_async_context(stdio_client(params))
#         print("DEBUG: stdio_client context entered.")
#         session = await self.exit_stack.enter_async_context(ClientSession(read, write))
#         print("DEBUG: ClientSession context entered, initializing session.")
#         await session.initialize()
#         print("DEBUG: Session initialized.")
#         return session

#     async def connect(self):
#         print("DEBUG: Starting connection to all MCP servers.")
#         # connect to each server
#         for spec in self.servers:
#             print(f"DEBUG: Connecting to server: {spec.path}")
#             session = await self._connect_one(spec)
#             self.sessions.append(session)
#             print(f"DEBUG: Connected to server: {spec.path}")

#         # load tools
#         loaded_tools = []
#         for s in self.sessions:
#             print("DEBUG: Loading MCP tools for session.")
#             maybe = load_mcp_tools(s)
#             ts = await maybe if asyncio.iscoroutine(maybe) else maybe
#             if ts:
#                 print(f"DEBUG: Loaded {len(ts)} tools.")
#                 loaded_tools.extend(ts)
#             else:
#                 print("DEBUG: No tools loaded for this session.")

#         if not loaded_tools:
#             print("DEBUG: No MCP tools found after loading all sessions.")
#             raise RuntimeError("No MCP tools found")
#         self.tools = loaded_tools
#         print(f"DEBUG: Total tools loaded: {len(self.tools)}")

#         # instantiate LLM
#         print(f"DEBUG: Instantiating LLM with model: {DEFAULT_MODEL}")
#         llm = ChatOpenAI(model=DEFAULT_MODEL, temperature=0)

#         # state_modifier guidance
       
#         state_modifier_text = """
# You are an Energy Data Analysis Agent for Integrum Energy Infrastructure Ltd. 
# Your role is to help create and maintain dynamic, scalable dashboards using MCP-Vizro 
# for solar and wind plants. 

# Guidelines:
# - Follow the ReAct framework: reason about user queries, use MCP tools for data, 
#   and provide clear, concise answers without exposing reasoning or scratchpads.
# - Tailor responses for professional energy analysis use cases, ensuring accuracy and 
#   clarity for both technical and business stakeholders.
# - When users request dashboards, design them to be:
#   • Dynamic: support real-time or near real-time data updates.
#   • Scalable: handle multiple plants, large datasets, and varying date ranges.
#   • Interactive: include filters (e.g., plant, date range, energy type).
#   • Actionable: highlight KPIs, anomalies, and areas needing review or approval.

# Energy KPIs to prioritize:
# - Solar: Generation (daily, monthly, YTD), Performance Ratio (PR), POA Irradiance
# - Wind: Generation, Wind Speed, Machine Availability (MA%)
# - Cross-plant: Comparisons, trends, alerts for underperformance

# When tools return data:
# - Summarize insights clearly
# - Provide dashboard-ready structures (tables, KPIs, charts)
# - Ensure results can plug directly into MCP-Vizro for visualization

# Tone:
# - Professional, accurate, and concise.
# - Write outputs as if they will be shown directly on an enterprise-grade dashboard.
# """

#         # create the agent
#         print("DEBUG: Creating LangGraph agent.")
#         self.app = create_react_agent(
#             model=llm,
#             tools=self.tools,
#             checkpointer=None,
#             prompt=state_modifier_text,
#             # recursion_limit=50,
#         )
#         print("DEBUG: LangGraph agent created.")

#     async def ainvoke(self, user_text: str) -> str:
#         print(f"DEBUG: ainvoke called with user_text: {user_text}")
#         if self.app is None:
#             print("DEBUG: self.app is None, not connected.")
#             raise RuntimeError("Client not connected. Call connect() first.")

#         # include history
#         thread_history = self.memory.get(self.thread_id, []) or []
#         print(f"DEBUG: Current thread_history length: {len(thread_history)}")
#         messages_for_state = [(e.get("role", "assistant"), e.get("content", "")) for e in thread_history]
#         messages_for_state.append(("user", user_text))

#         state = {"messages": messages_for_state}
#         config = {"configurable": {"thread_id": self.thread_id}}

#         print("DEBUG: Invoking agent with state and config.")
#         result = await self.app.ainvoke(state, config=config)
#         print(f"DEBUG: Agent invocation result: {type(result)}")

#         # Normalize result
#         content = "(no output)"
#         if isinstance(result, dict):
#             if "messages" in result and isinstance(result["messages"], list):
#                 messages = result["messages"]
#                 last = messages[-1]
#                 print(f"DEBUG: Last message in result: {last}")
#                 if isinstance(last, BaseMessage):
#                     content = last.content
#                 elif isinstance(last, tuple) and len(last) >= 2:
#                     content = last[1]
#                 elif isinstance(last, dict):
#                     content = last.get("content") or last.get("text")
#                 else:
#                     content = str(last)
#             elif "output" in result:
#                 content = str(result["output"])
#             elif "text" in result:
#                 content = str(result["text"])
#         elif isinstance(result, str):
#             content = result

#         print(f"DEBUG: Normalized content: {content}")

#         # Persist history
#         now = datetime.now().astimezone().isoformat()
#         thread_history.append({"role": "user", "content": user_text, "timestamp": now})
#         thread_history.append({"role": "assistant", "content": content, "timestamp": now})

#         self.memory[self.thread_id] = thread_history
#         self.memory["thread_id"] = self.thread_id
#         self.save_memory()

#         return content

#     async def reset_history(self):
#         print("DEBUG: Resetting history for thread_id:", self.thread_id)
#         self.memory[self.thread_id] = []
#         self.save_memory()

#     async def close(self):
#         print("DEBUG: Closing all async contexts and saving memory.")
#         await self.exit_stack.aclose()
#         self.save_memory()

#     def _format_timestamp(self, iso_ts: Optional[str]) -> str:
#         if not iso_ts:
#             return ""
#         try:
#             dt = datetime.fromisoformat(iso_ts)
#             return dt.strftime("%Y-%m-%d %H:%M:%S %z").strip()
#         except Exception as e:
#             print(f"DEBUG: Failed to format timestamp: {e}")
#             return iso_ts

#     def format_history(self, last_n: Optional[int] = None) -> str:
#         print(f"DEBUG: Formatting history, last_n={last_n}")
#         hist = self.memory.get(self.thread_id, []) or []
#         if last_n is not None:
#             hist = hist[-last_n:]
#         lines = []
#         for i, entry in enumerate(hist, start=1):
#             role = entry.get("role", "assistant").upper()
#             ts = self._format_timestamp(entry.get("timestamp", ""))
#             header = f"{i:03d} | {role:9} | {ts}" if ts else f"{i:03d} | {role:9}"
#             body = entry.get("content", "")
#             lines.append(header)
#             lines.append(body)
#             lines.append("-" * 80)
#         return "\n".join(lines) if lines else "(no history)"


# # ----------------------------
# # CLI entrypoint
# # ----------------------------
# async def main():
#     print("DEBUG: Starting main CLI entrypoint.")
#     if len(sys.argv) < 2:
#         print("Usage: python client.py /path/to/mcp_server.py [...]")
#         sys.exit(1)

#     servers = [MCPServerSpec(path=p) for p in sys.argv[1:]]
#     print(f"DEBUG: Parsed server specs: {servers}")
#     client = MCPMultiServerClient(servers=servers)

#     try:
#         print("DEBUG: Connecting client to servers.")
#         await client.connect()
#         print(f"\nLangGraph MCP Agent ready. Thread ID: {client.thread_id}")
#         print("Commands: 'history', 'history <n>', 'history json', 'reset', 'quit'\n")

#         while True:
#             try:
#                 q = input("Query> ").strip()
#             except (EOFError, KeyboardInterrupt):
#                 print("\nExiting.")
#                 break

#             if not q:
#                 continue

#             if q.lower() in {"quit", "exit"}:
#                 print("DEBUG: Quit command received, exiting loop.")
#                 break

#             if q.lower().startswith("history"):
#                 print("DEBUG: History command received:", q)
#                 parts = q.split()
#                 if len(parts) == 1:
#                     print(client.format_history() + "\n")
#                 elif len(parts) == 2 and parts[1].isdigit():
#                     print(client.format_history(int(parts[1])) + "\n")
#                 elif len(parts) == 2 and parts[1].lower() == "json":
#                     print(json.dumps(client.memory.get(client.thread_id, []), indent=2) + "\n")
#                 else:
#                     print("Usage: history | history <n> | history json\n")
#                 continue

#             if q.lower() == "reset":
#                 print("DEBUG: Reset command received.")
#                 await client.reset_history()
#                 print("History cleared for this thread.\n")
#                 continue

#             try:
#                 print(f"DEBUG: User query: {q}")
#                 answer = await client.ainvoke(q)
#                 print("\nAssistant:", answer, "\n")
#             except Exception as e:
#                 print(f"DEBUG: Error in ainvoke: {e}\n")

#     finally:
#         print("DEBUG: Closing client.")
#         await client.close()


# if __name__ == "__main__":
#     asyncio.run(main())



import asyncio
import os
import sys
import uuid
import json
from contextlib import AsyncExitStack
from dataclasses import dataclass
from typing import List, Optional
from datetime import datetime

from dotenv import load_dotenv

# --- MCP core (Python SDK) ---
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# --- LangChain / LangGraph stack ---
from langchain_openai import ChatOpenAI
from langchain_mcp_adapters.tools import load_mcp_tools
from langgraph.prebuilt import create_react_agent
from langchain.schema import BaseMessage



# --- Gemini via LangChain ---
# from langchain_google_genai import ChatGoogleGenerativeAI



# ----------------------------
# Logging helper
# ----------------------------
def log(msg: str, phase: str = "GENERAL", level: str = "DEBUG", success: bool = None):
    """Consistent logging with timestamp, phase, and success/failure markers."""
    ts = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    status = ""
    if success is True:
        status = "✅ "
    elif success is False:
        status = "❌ "
    print(f"[{ts}] [{level}] [{phase}] {status}{msg}")


# ----------------------------
# Config & Environment
# ----------------------------
load_dotenv()
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY not found in environment variables.")

DEFAULT_MODEL = os.getenv("OPENAI_MODEL", "gpt-4o")


# GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
# if not GEMINI_API_KEY:
#     raise ValueError("GEMINI_API_KEY not found in environment variables.")

# DEFAULT_MODEL = os.getenv("GEMINI_MODEL", "gemini-2.0-flash-exp")


DEFAULT_MEMORY_FILE = os.getenv("MCP_CLIENT_MEMORY_FILE", "mcp_client_memory.json")

from langchain.callbacks.base import BaseCallbackHandler

class ToolLoggingCallback(BaseCallbackHandler):
    def on_tool_start(self, serialized, input_str, **kwargs):
        tool_name = serialized.get("name", "unknown_tool")
        print(f"\n🛠️ LLM called tool: {tool_name}")
        print(f"   🔹 Input: {input_str}")

    def on_tool_end(self, output, **kwargs):
        print(f"   🔸 Output: {output}\n")


@dataclass
class MCPServerSpec:
    """Defines an MCP server for stdio connection."""
    path: str
    args: Optional[List[str]] = None

    @property
    def command(self) -> str:
        return "python" if self.path.endswith(".py") else "node"

    @property
    def full_args(self) -> List[str]:
        return [self.path] + (self.args or [])


class MCPMultiServerClient:
    def __init__(self, servers: List[MCPServerSpec], thread_id: Optional[str] = None):
        log("Initializing MCPMultiServerClient", "INIT")
        log(f"Configured servers: {[s.path for s in servers]}", "INIT")

        self.servers = servers
        self.exit_stack = AsyncExitStack()
        self.sessions: List[ClientSession] = []
        self.tools = []
        self.app = None

        # Setup memory file
        self.memory_file = DEFAULT_MEMORY_FILE
        log(f"Using memory file: {self.memory_file}", "MEMORY")
        os.makedirs(os.path.dirname(self.memory_file) or ".", exist_ok=True)

        if os.path.exists(self.memory_file):
            try:
                with open(self.memory_file, "r", encoding="utf-8") as f:
                    self.memory = json.load(f)
                log("Loaded memory from file", "MEMORY", success=True)
            except Exception as e:
                log(f"Failed to load memory file: {e}", "MEMORY", success=False)
                self.memory = {}
        else:
            log("No memory file found, starting fresh", "MEMORY")
            self.memory = {}

        # ✅ Use thread_id from constructor OR memory OR generate new
        if thread_id:
            self.thread_id = thread_id
        else:
            self.thread_id = self.memory.get("thread_id", str(uuid.uuid4()))

        log(f"Using thread_id: {self.thread_id}", "MEMORY")
        self.memory.setdefault(self.thread_id, [])
        self.memory["thread_id"] = self.thread_id
        self.save_memory()


    def save_memory(self):
        log("Saving memory to file", "MEMORY")
        with open(self.memory_file, "w", encoding="utf-8") as f:
            json.dump(self.memory, f, indent=2)

    async def _connect_one(self, spec: MCPServerSpec) -> ClientSession:
        log(f"Connecting to MCP server {spec.path} with args {spec.full_args}", "CONNECT")
        params = StdioServerParameters(command=spec.command, args=spec.full_args)
        read, write = await self.exit_stack.enter_async_context(stdio_client(params))
        session = await self.exit_stack.enter_async_context(ClientSession(read, write))
        await session.initialize()
        log(f"Session initialized for {spec.path}", "CONNECT", success=True)
        return session

    async def connect(self):
        log("Starting connection to all MCP servers", "CONNECT")
        for spec in self.servers:
            session = await self._connect_one(spec)
            self.sessions.append(session)

        loaded_tools = []
        
        for s in self.sessions:
            log("Loading MCP tools", "TOOLS")
            maybe = load_mcp_tools(s)
            ts = await maybe if asyncio.iscoroutine(maybe) else maybe
            if ts:
               
                log(f"Loaded {len(ts)} tools", "TOOLS", success=True)
                loaded_tools.extend(ts)
            else:
                log("No tools loaded for this session", "TOOLS")

        if not loaded_tools:
            log("No MCP tools found in any session", "TOOLS", success=False)
            raise RuntimeError("No MCP tools found")

        self.tools = loaded_tools
        log(f"Tools available: {[t.name for t in self.tools]}", "TOOLS", success=True)
        log(f"Total tools loaded: {len(self.tools)}", "TOOLS", success=True)

        # Instantiate LLM
        log(f"Instantiating LLM with model {DEFAULT_MODEL}", "LLM")
        llm = ChatOpenAI(model=DEFAULT_MODEL, temperature=0, callbacks=[ToolLoggingCallback()])
        # llm = ChatGoogleGenerativeAI(
        #     model=DEFAULT_MODEL,
        #     temperature=0,
        #     google_api_key=GEMINI_API_KEY,
        #     callbacks=[ToolLoggingCallback()]
        # )

        # Attach callback to tools as well
        for tool in loaded_tools:
            tool.callbacks = [ToolLoggingCallback()]

        tool_descriptions = []
        for t in loaded_tools:
            # Each MCP tool has a name + description
            desc = getattr(t, "description", "") or "No description provided"
            tool_descriptions.append(f"- **{t.name}**: {desc}")

        tools_block = "\n".join(tool_descriptions)

        # Agent prompt
      
        system_prompt = f"""
You are an **Energy Data Analysis Agent** at **Integrum Energy Infrastructure Ltd.**, 
powered by MCP-provided tools.

📱 This session is linked to a WhatsApp customer with mobile number: **{self.thread_id}**.  
- This number uniquely identifies the customer.  
- Always use it implicitly for plant-specific lookups.  
- **Never expose or repeat the mobile number** — instead, refer to the customer by their registered contact name.

The following tools are available (discovered from the MCP servers at runtime):

{tools_block}

---

### 🎯 Your Objectives:
1. **Clarify intent first** – ask follow-up questions when necessary.  
2. **Prioritize tool usage** – always rely on MCP tools when available.  
3. **Communicate professionally** – provide concise, structured responses (bullet points, tables, or JSON if suitable).  
4. **Explain reasoning briefly** before making tool calls.  
5. **Tool failures** – acknowledge the issue (e.g., “I wasn’t able to fetch that data just now. Would you like me to retry or check another metric?”).  
6. **Respect confidentiality** – never reveal internal identifiers (e.g., `plant_id`, mobile numbers). Use only `plant_long_name` in customer-facing answers.  
7. **Temporal limitation** – you cannot provide "today’s" values (e.g., today’s generation). You may only report **yesterday or earlier**.  
8. **Multi-plant handling** –  
   - Use `get_plants_by_mobile_number` to check if this customer has more than one plant.  
   - If a query (e.g., "What was yesterday’s generation?") applies to all plants, first provide the **total combined generation**.  
   - Then politely ask: *“Would you like me to show the breakdown by each plant as well?”*  
   - Only show the breakdown if the customer requests it.  
   - ⚡ **Important:** The same WhatsApp number may be linked to multiple plants. Each plant has a unique `plant_id`, which must be respected for accurate tool lookups. 
9. **Tone** – maintain a polite, professional style. Default to short answers unless detail is requested.  
10. **Greet the customer by `contact_person`** if available; otherwise, keep greeting generic.  
---
"""

        self.app = create_react_agent(
            model=llm,
            tools=self.tools,
            checkpointer=None,
            prompt=system_prompt
            
        )
        log("LangGraph agent created successfully", "LLM", success=True)

    async def ainvoke(self, user_text: str) -> str:
        log(f"ainvoke called with query: {user_text}", "INVOKE")
        if self.app is None:
            log("Client not connected, app is None", "INVOKE", success=False)
            raise RuntimeError("Client not connected. Call connect() first.")

        thread_history = self.memory.get(self.thread_id, []) or []
        log(f"Thread history length: {len(thread_history)}", "INVOKE")

        messages_for_state = [(e.get("role", "assistant"), e.get("content", "")) for e in thread_history]
        messages_for_state.append(("user", user_text))
        state = {"messages": messages_for_state}
        config = {"configurable": {"thread_id": self.thread_id}}

        result = await self.app.ainvoke(state, config=config)
        log(f"Agent invocation completed, result type: {type(result)}", "INVOKE", success=True)
        log(f"Agent invocation completed, result: {result}", "INVOKE", success=True)


  

        
        # Normalize result
        content = "(no output)"
        if isinstance(result, dict):
            if "messages" in result and isinstance(result["messages"], list):
                last = result["messages"][-1]
         
                
                if isinstance(last, BaseMessage):
                    content = last.content
                elif isinstance(last, tuple) and len(last) >= 2:
                    content = last[1]
                elif isinstance(last, dict):
                    content = last.get("content") or last.get("text")
                else:
                    content = str(last)
            elif "output" in result:
                csv_file_path = result["csv_file_path"]
                print(f"csv_file_path: {csv_file_path}")
                content = str(result["output"])
            elif "text" in result:
                content = str(result["text"])
        elif isinstance(result, str):
            content = result

        log(f"Normalized content: {content}", "INVOKE")

        # Persist history
        now = datetime.now().astimezone().isoformat()
        thread_history.append({"role": "user", "content": user_text, "timestamp": now})
        thread_history.append({"role": "assistant", "content": content, "timestamp": now})

        self.memory[self.thread_id] = thread_history
        self.memory["thread_id"] = self.thread_id
        self.save_memory()

        return content

    async def reset_history(self):
        log(f"Resetting history for thread_id {self.thread_id}", "HISTORY")
        self.memory[self.thread_id] = []
        self.save_memory()

    async def close(self):
        log("Closing all async contexts and saving memory", "CLOSE")
        await self.exit_stack.aclose()
        self.save_memory()

    def _format_timestamp(self, iso_ts: Optional[str]) -> str:
        if not iso_ts:
            return ""
        try:
            dt = datetime.fromisoformat(iso_ts)
            return dt.strftime("%Y-%m-%d %H:%M:%S %z").strip()
        except Exception as e:
            log(f"Failed to format timestamp: {e}", "HISTORY", success=False)
            return iso_ts

    def format_history(self, last_n: Optional[int] = None) -> str:
        hist = self.memory.get(self.thread_id, []) or []
        if last_n is not None:
            hist = hist[-last_n:]
        lines = []
        for i, entry in enumerate(hist, start=1):
            role = entry.get("role", "assistant").upper()
            ts = self._format_timestamp(entry.get("timestamp", ""))
            header = f"{i:03d} | {role:9} | {ts}" if ts else f"{i:03d} | {role:9}"
            body = entry.get("content", "")
            lines.append(header)
            lines.append(body)
            lines.append("-" * 80)
        return "\n".join(lines) if lines else "(no history)"


# ----------------------------
# CLI entrypoint
# ----------------------------
async def main():
    # log("Starting CLI entrypoint", "CLI")
    # # if len(sys.argv) < 2:
    # #     print("Usage: python client.py /path/to/mcp_server.py [...]")
    # #     sys.exit(1)

    # # servers = [MCPServerSpec(path=p) for p in sys.argv[1:]]
    # # log(f"Parsed server specs: {[s.path for s in servers]}", "CLI")
    log("Starting CLI entrypoint", "CLI")
    servers = [MCPServerSpec(path="server.py")]

    # client = MCPMultiServerClient(servers=servers)
    client = MCPMultiServerClient(servers=servers, thread_id="9739688666")

    try:
        log("Connecting client to servers", "CLI")
        await client.connect()
        print(f"\nLangGraph MCP Agent ready. Thread ID: {client.thread_id}")
        print("Commands: 'history', 'history <n>', 'history json', 'reset', 'quit'\n")

        while True:
            try:
                q = input("Query> ").strip()
            except (EOFError, KeyboardInterrupt):
                log("User terminated session", "CLI", success=False)
                break

            if not q:
                continue

            if q.lower() in {"quit", "exit"}:
                log("Quit command received", "CLI")
                break

            if q.lower().startswith("history"):
                log(f"History command: {q}", "HISTORY")
                parts = q.split()
                if len(parts) == 1:
                    print(client.format_history() + "\n")
                elif len(parts) == 2 and parts[1].isdigit():
                    print(client.format_history(int(parts[1])) + "\n")
                elif len(parts) == 2 and parts[1].lower() == "json":
                    print(json.dumps(client.memory.get(client.thread_id, []), indent=2) + "\n")
                else:
                    print("Usage: history | history <n> | history json\n")
                continue

            if q.lower() == "reset":
                await client.reset_history()
                print("History cleared for this thread.\n")
                continue

            try:
                answer = await client.ainvoke(q)
                print("\nAssistant:", answer, "\n")
            except Exception as e:
                log(f"Error in ainvoke: {e}", "INVOKE", success=False)

    finally:
        log("Closing client", "CLOSE")
        await client.close()




if __name__ == "__main__":
    asyncio.run(main())
