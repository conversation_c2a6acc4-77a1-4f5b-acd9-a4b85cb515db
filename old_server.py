# SCHEMA_FILE = os.getenv("SCHEMA_FILE", "solar_schema.json")

# def load_schema() -> str:
#     """
#     Load schema JSON as a formatted string.
#     Returns a pretty-printed JSON string or error message.
#     """
#     if not os.path.exists(SCHEMA_FILE):
#         logger.warning(f"Schema file not found at {SCHEMA_FILE}")
#         return "Schema file not found."
#     try:
#         with open(SCHEMA_FILE, "r", encoding="utf-8") as f:
#             return json.dumps(json.load(f), indent=2)
#     except Exception as e:
#         logger.error(f"Failed to load schema: {e}")
        # return f"Error loading schema: {e}"

# @mcp.tool(
#     name="generate_sql_query",
#     description="Generates an optimized SQL query based on a natural language question and plant name."
# )
# def generate_sql_query(question: str, plant_name: str) -> str:
#     """
#     Generates an optimized SQL query based on a natural language question and plant name.
#     Returns only the SQL query string (no additional commentary).
#     """
#     now = datetime.now()
#     schema = load_schema()
#     return f"""
# You are an expert SQL generator.
# Translate the natural language question into a valid, optimized SQL query 
# for the `dgr_solar_db` table.

# ---

# ### Question Context:
# - Natural language question: "{question}"
# - Plant short code: "{plant_name}"
# - Standard prefix: "{PLANT_PREFIX}"
# - Query generated at: {now.strftime('%Y-%m-%d %H:00')}

# ---

# ### Schema Context:
# Here is the schema definition you must rely on:
# {schema}

# Important fields:
# - `date`: Date of the report (YYYY-MM-DD)
# - `plant_short_name`: Unique short code for plant (always prefixed with {PLANT_PREFIX})
# - `generation`: Daily generation (kWh)
# - `pr`: Performance ratio (%)
# - `poa`: Plane of array irradiance (kWh/m²)
# - `*_monthly`: Month-to-date fields
# - `edit_*`: Edited values
# - `approved`, `review`, `status`: Workflow flags

# ---

# ### Guidelines:
# 1. Always include `plant_short_name` and `plant_long_name` in the SELECT clause of every query, unless they are already present.
# 2. Always filter by **plant_short_name = CONCAT('{PLANT_PREFIX}', '{plant_name}')**.
#    - Example: if input is `KIDS`, then filter with `plant_short_name = 'IN.INTE.KIDS'`.
# 3. Use `date` for daily filtering (`CURRENT_DATE()`, `CURRENT_DATE() - 1`, ranges).
# 4. For month-level metrics, use `*_monthly` fields.
# 5. For edited values, use `edit_*` columns.
# 6. Always alias outputs clearly (`AS DAILY_GENERATION`, `AS AVG_PR`, etc.).
# 7. If aggregation is required, group by `date` (unless monthly aggregate).
# 8. Return only SQL code – no explanations.

# ---

# ### Example Conversions:

# #### Input 1:
# Question: What is yesterday’s generation?  
# Plant_Name: "KIDS"

# #### Output 1:
# ```sql
# SELECT plant_short_name, plant_long_name, date, generation AS DAILY_GENERATION
# FROM dgr_solar_db
# WHERE plant_short_name = 'IN.INTE.KIDS'
#   AND date = CURRENT_DATE() - 1;
# ```

# ### Input 2:

# Question: Show me this month’s average PR
# Plant_Name: "IN.INTE.KIDS"

# ### Output 2:
# ```sql
# SELECT plant_short_name, plant_long_name, AVG(pr_monthly) AS AVG_PR_THIS_MONTH
# FROM dgr_solar_db
# WHERE plant_short_name = 'IN.INTE.KIDS'
#   AND EXTRACT(YEAR FROM date) = YEAR(CURRENT_DATE())
#   AND EXTRACT(MONTH FROM date) = MONTH(CURRENT_DATE());
# ```

# ### Input 3:
# Question: Give me last 7 days POA trend
# Plant_Name: "DELHI_SITE"

# ### Output 3:
# ```sql
# SELECT plant_short_name, plant_long_name, date, poa AS DAILY_POA
# FROM dgr_solar_db
# WHERE plant_short_name = 'IN.INTE.DELHI_SITE'
#   AND date >= CURRENT_DATE() - 7
# ORDER BY date ASC;
# ```
# """



# @mcp.tool(
#     name="vizro_generation_dashboard",
#     description="Creates a Vizro dashboard showing daily generation trends for a given dataset."
# )
# def vizro_generation_dashboard(json_records: str) -> str:
#     # Pass JSON string directly (function handles parsing internally)
#     dashboard = generation_trend_dashboard(json_records)
#     return json.dumps(dashboard, indent=2)


# @mcp.tool(
#     name="vizro_performance_dashboard",
#     description="Creates a Vizro dashboard for performance KPIs (PR, POA, etc.)."
# )
# def vizro_performance_dashboard(json_records: str) -> str:
#     # Function expects DataFrame, so we convert here
#     data = pd.DataFrame(json.loads(json_records))
#     dashboard = performance_kpi_dashboard(data)
#     return json.dumps(dashboard, indent=2)


# @mcp.tool(
#     name="vizro_portfolio_dashboard",
#     description="Creates a Vizro dashboard providing a portfolio-level overview across plants."
# )
# def vizro_portfolio_dashboard(json_records: str) -> str:
#     # Function expects DataFrame, so we convert here
#     data = pd.DataFrame(json.loads(json_records))
#     dashboard = portfolio_overview_dashboard(data)
#     return json.dumps(dashboard, indent=2)

