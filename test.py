# # import pandas as pd

# # def normalize_number(number):
# #     """Normalize number by removing spaces, dashes, and country codes as needed"""
# #     return ''.join(filter(str.isdigit, str(number)))

# # def get_metadata_by_contact_number(file_path, input_number):
# #     """
# #     Given a contact number, return ALL plant metadata rows from the CSV where 
# #     input_number matches Contact Number or Test Number.
# #     If found in Contact Number, map number to the corresponding contact person.
# #     Returns a list of all matching rows.
# #     """
# #     input_number = normalize_number(input_number)
# #     df = pd.read_csv(file_path)
# #     results = []

# #     for _, row in df.iterrows():
# #         contact_numbers_raw = str(row.get('Contact Number', ''))
# #         test_numbers_raw = str(row.get('Test Number', ''))
# #         contact_persons_raw = str(row.get('Contact Person', ''))

# #         # Normalize and split contact/test numbers
# #         contact_numbers_list = [normalize_number(num) for num in contact_numbers_raw.split(',') if normalize_number(num)]
# #         test_numbers_list = [normalize_number(num) for num in test_numbers_raw.split(',') if normalize_number(num)]
# #         contact_persons_list = [name.strip() for name in contact_persons_raw.split(',')]

# #         all_numbers = contact_numbers_list + test_numbers_list
# #         if input_number in all_numbers:
# #             # Map contact number to corresponding contact person by index
# #             if input_number in contact_numbers_list:
# #                 idx = contact_numbers_list.index(input_number)
# #                 matched_person = contact_persons_list[idx] if idx < len(contact_persons_list) else (contact_persons_list[-1] if contact_persons_list else "")
# #             else:
# #                 matched_person = "Test Number Contact"

# #             results.append({
# #                 "plant_id": str(row.get('Plant id', '')).strip(),
# #                 "client_name": str(row.get('Customer Name', '')).strip(),
# #                 "type": str(row.get('Type', '')).strip(),
# #                 "capacity": row.get('Capacity ( MW)', ''),
# #                 "combined": row.get('Combined', ''),
# #                 "turbine_metadata": str(row.get('Turbine Metadata', '')).strip(),
# #                 "contact_person": matched_person,
# #                 "contact_number": contact_numbers_raw.strip(),
# #                 "test_number": test_numbers_raw.strip()
# #             })

# #     return results
import os
import re
import json
import pandas as pd
from datetime import datetime
from typing import Any, List, Dict, Optional

CUSTOMER_CSV = "customer_data - Sheet1.csv"


def normalize_number(number: str) -> str:
    """Remove all non-digits from a phone number string."""
    return re.sub(r"\D", "", str(number).strip()) if number else ""


# def get_metadata_by_contact_number(input_number: str) -> List[Dict[str, Any]]:
#     file_path = CUSTOMER_CSV
#     print(f"🔍 Entered get_metadata_by_contact_number with file={file_path}, number={input_number}")

#     # Normalize input number
#     input_number = normalize_number(input_number)
#     print(f"📱 Normalized input number: {input_number}")

#     # Load CSV
#     df = pd.read_csv(file_path, dtype=str).fillna("")
#     print(f"📊 CSV loaded with {df.shape[0]} rows and {df.shape[1]} columns")

#     results: List[Dict[str, Any]] = []

#     for _, row in df.iterrows():
#         contact_numbers_raw = str(row.get("Contact Number", ""))
#         test_numbers_raw = str(row.get("Test Number", ""))
#         contact_persons_raw = str(row.get("Contact Person", ""))

#         # Normalize and split contact/test numbers
#         contact_numbers_list = [normalize_number(num) for num in contact_numbers_raw.split(",") if normalize_number(num)]
#         test_numbers_list = [normalize_number(num) for num in test_numbers_raw.split(",") if normalize_number(num)]
#         contact_persons_list = [name.strip() for name in contact_persons_raw.split(",") if name.strip()]

#         all_numbers = contact_numbers_list + test_numbers_list
#         if input_number in all_numbers:
#             # Map contact number to corresponding contact person
#             if input_number in contact_numbers_list:
#                 idx = contact_numbers_list.index(input_number)
#                 matched_person = (
#                     contact_persons_list[idx]
#                     if idx < len(contact_persons_list)
#                     else (contact_persons_list[-1] if contact_persons_list else "")
#                 )
#             else:
#                 matched_person = "Test Number Contact"

#             result = {
#                 "plant_id": str(row.get("Plant id", "")).strip(),
#                 "client_name": str(row.get("Customer Name", "")).strip(),
#                 "type": str(row.get("Type", "")).strip(),
#                 "capacity": str(row.get("Capacity ( MW)", "")).strip(),
#                 "combined": str(row.get("Combined", "")).strip(),
#                 "turbine_metadata": str(row.get("Turbine Metadata", "")).strip(),
#                 "contact_person": matched_person,
#                 "contact_number": contact_numbers_raw.strip(),
#                 "test_number": test_numbers_raw.strip(),
#             }
#             results.append(result)
#             print(f"➕ Added result: {result}")

#     if not results:
#         print(f"⚠️ No matches found for number: {input_number}")

#     print(f"🏁 Final results: {results}")
#     return results


# results = get_metadata_by_contact_number("8867855303")




def get_metadata_by_contact_number(input_number: str) -> List[Dict[str, Any]]:
    """
    Retrieves comprehensive metadata for renewable energy plants by searching the customer CSV for records where the provided contact number matches entries in the 'Contact Number' or 'Test Number' columns.

    Args:
        input_number (str): The contact number to search for. Can include spaces, dashes, or other non-digit characters.

    Returns:
        List[Dict[str, Any]]: A list of dictionaries, each containing metadata for a plant associated with the input number.
        Each dictionary includes:
            - 'plant_id' (str): The plant's unique identifier.
            - 'client_name' (str): The name of the customer/client.
            - 'type' (str): The type of plant (e.g., 'solar', 'wind').
            - 'capacity' (str): The plant's capacity in MW.
            - 'combined' (str): Combined plant information, if available.
            - 'turbine_metadata' (str): Turbine metadata, if available.
            - 'contact_person' (str): The mapped contact person for the matched number, or a default if not found.
            - 'contact_number' (str): The raw contact number(s) from the CSV.
            - 'test_number' (str): The raw test number(s) from the CSV.

    Example:
        >>> get_metadata_by_contact_number("9876543210")
        [
            {
                "plant_id": "IN.INTE.KIDS",
                "client_name": "ABC Energy",
                "type": "solar",
                "capacity": "5",
                "combined": "",
                "turbine_metadata": "",
                "contact_person": "John Doe",
                "contact_number": "9876543210",
                "test_number": ""
            }
        ]

    Notes:
        - Handles multiple matches and ensures contact person mapping if found in 'Contact Number'.
        - If the input number is found only in 'Test Number', 'contact_person' is set to "Test Number Contact".
        - Returns an empty list if no matches are found.

    """
    file_path = CUSTOMER_CSV

    # Normalize input number
    input_number = normalize_number(input_number)

    # Load CSV
    df = pd.read_csv(file_path, dtype=str).fillna("")

    results: List[Dict[str, Any]] = []

    for _, row in df.iterrows():
        contact_numbers_raw = str(row.get("Contact Number", ""))
        test_numbers_raw = str(row.get("Test Number", ""))
        contact_persons_raw = str(row.get("Contact Person", ""))

        # Normalize and split contact/test numbers
        contact_numbers_list = [normalize_number(num) for num in contact_numbers_raw.split(",") if normalize_number(num)]
        test_numbers_list = [normalize_number(num) for num in test_numbers_raw.split(",") if normalize_number(num)]
        contact_persons_list = [name.strip() for name in contact_persons_raw.split(",") if name.strip()]

        all_numbers = contact_numbers_list + test_numbers_list
        if input_number in all_numbers:
            # Map contact number to corresponding contact person
            if input_number in contact_numbers_list:
                idx = contact_numbers_list.index(input_number)
                matched_person = (
                    contact_persons_list[idx]
                    if idx < len(contact_persons_list)
                    else (contact_persons_list[-1] if contact_persons_list else "")
                )
            else:
                matched_person = "Test Number Contact"

            result = {
                "plant_id": str(row.get("Plant id", "")).strip(),
                "client_name": str(row.get("Customer Name", "")).strip(),
                "type": str(row.get("Type", "")).strip(),
                "capacity": str(row.get("Capacity ( MW)", "")).strip(),
                "combined": str(row.get("Combined", "")).strip(),
                "turbine_metadata": str(row.get("Turbine Metadata", "")).strip(),
                "contact_person": matched_person,
                "contact_number": contact_numbers_raw.strip(),
                "test_number": test_numbers_raw.strip(),
            }
            results.append(result)

    if not results:
        return []


    return results



results = get_metadata_by_contact_number("8867855303")
print("results server")
print(results)


from server import fetch_data_from_db

data = fetch_data_from_db("""SELECT date, elem->>'Loc No' AS turbine_id, AVG(NULLIF(elem->>'Avg Wind Speed','')::FLOAT) AS avg_wind_speed, SUM(NULLIF(elem->>'Daily Generation (kWh)','')::FLOAT) AS daily_generation FROM dgr_wind_db, jsonb_array_elements(edit_csv_report_data) elem WHERE plant_short_name = 'IN.INTE.ANSP' AND date BETWEEN '2023-10-14' AND '2023-10-23' GROUP BY date, turbine_id ORDER BY date ASC;
""")
print(data)