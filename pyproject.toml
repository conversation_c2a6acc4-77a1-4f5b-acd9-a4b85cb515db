[project]
name = "mcp-vizro-dashboard"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastmcp>=2.11.3",
    "langchain>=0.3.27",
    "langchain-google-genai>=2.1.10",
    "langchain-mcp-adapters>=0.1.9",
    "langchain-openai>=0.3.31",
    "langgraph>=0.6.6",
    "langgraph-checkpoint>=2.1.1",
    "langgraph-checkpoint-sqlite>=2.0.11",
    "matplotlib>=3.10.6",
    "mcp[cli]>=1.13.1",
    "pandas>=2.3.2",
    "pymysql>=1.1.2",
    "pytest>=8.4.1",
    "pyyaml>=6.0.2",
    "seaborn>=0.13.2",
    "uvicorn>=0.35.0",
    "vizro>=0.1.44",
]
