


# vizro_dashboards.py
import os
import json
import pandas as pd
from datetime import datetime
from typing import Dict, Any
import vizro.models as vzm
import vizro.plotly.express as px
import logging

ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

# =========================
# Logging Setup
# =========================
logger = logging.getLogger("vizro_dashboards")
LOG_LEVEL = os.getenv("MCP_LOG_LEVEL", "INFO").upper()

# Console handler
console_handler = logging.StreamHandler()
formatter = logging.Formatter("%(asctime)s %(levelname)s [%(name)s] %(message)s")
console_handler.setFormatter(formatter)

# File handler
log_file = os.path.join(ROOT_DIR, "logs", "vizro_dashboards.log")
os.makedirs(os.path.dirname(log_file), exist_ok=True)
file_handler = logging.FileHandler(log_file, mode="a", encoding="utf-8")
file_handler.setFormatter(formatter)

if not logger.hasHandlers():
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
logger.setLevel(LOG_LEVEL)

# =========================
# Column Normalizer
# =========================
def normalize_columns(df: pd.DataFrame) -> pd.DataFrame:
    """
    Normalize SQL alias outputs into consistent column names expected by dashboards.
    """
    mapping = {
        "DAILY_GENERATION": "generation",
        "GENERATION_TREND": "generation",
        "AVG_PR_THIS_MONTH": "pr",
        "PR_TREND": "pr",
        "DAILY_POA": "poa",
        "POA_TREND": "poa",
    }
    for alias, std in mapping.items():
        if alias in df.columns and std not in df.columns:
            df[std] = df[alias]
    return df

# =========================
# High-Level Dashboards
# =========================
def generation_trend_dashboard(json_records: str) -> Dict[str, Any]:
    """Wrapper tool: cleans DB output and creates generation trend dashboard."""
    logger.debug("Building Vizro dashboard from JSON records.")

    try:
        # Load records into DataFrame
        records = json.loads(json_records)
        data = pd.DataFrame(records)

        if data.empty:
            return {"status": "error", "message": "No data available for this query."}

        # ✅ Deduplicate rows by date + plant
        data = data.drop_duplicates(subset=["date", "plant_short_name"])

        # ✅ Replace NaN with 0 for JSON safety
        data = data.fillna(0)

        # ✅ Ensure date is parsed
        data["date"] = pd.to_datetime(data["date"]).dt.date

        # ✅ Select Y column
        y_col = "DAILY_GENERATION" if "DAILY_GENERATION" in data.columns else "generation"

        # ✅ Build Plotly line chart
        fig = px.line(
            data,
            x="date",
            y=y_col,
            color="plant_short_name",
            title="Daily Generation Trend"
        )

        # ✅ Use unique titles (avoid duplicate serialization issue)
        
        plant = data["plant_short_name"].iloc[0]

        dashboard = vzm.Dashboard(
            title=f"Generation Trends - {plant}",
            pages=[
                vzm.Page(
                    title="Daily Generation ",
                    components=[vzm.Graph(figure=fig)]
                )
            ]
        )

        logger.info("Vizro dashboard created successfully.")
        return json.loads(dashboard.model_dump_json(indent=2))

    except Exception as e:
        logger.exception("Error while building Vizro dashboard")
        return {"status": "error", "message": str(e)}


def performance_kpi_dashboard(data: pd.DataFrame) -> Dict[str, Any]:
    """Performance metrics dashboard (PR, POA)."""
    logger.debug("Raw columns before normalization: %s", list(data.columns))
    data = normalize_columns(data)
    logger.debug("Normalized columns: %s", list(data.columns))

    figs = []
    if "pr" in data.columns:
        figs.append(vzm.Graph(figure=px.line(
            data, x="date", y="pr", color="plant_short_name", title="Performance Ratio"
        )))
    else:
        logger.warning("PR column not found in data for performance_kpi_dashboard.")

    if "poa" in data.columns:
        figs.append(vzm.Graph(figure=px.line(
            data, x="date", y="poa", color="plant_short_name", title="Irradiance (POA)"
        )))
    else:
        logger.warning("POA column not found in data for performance_kpi_dashboard.")

    dashboard = vzm.Dashboard(
        title="Performance KPIs",
        pages=[vzm.Page(title="Performance Dashboard", components=figs)],
    )
    logger.info("Performance KPI dashboard built successfully.")
    return json.loads(dashboard.model_dump_json(indent=2))


def portfolio_overview_dashboard(data: pd.DataFrame) -> Dict[str, Any]:
    """Overview across all plants (generation vs PR)."""
    logger.debug("Raw columns before normalization: %s", list(data.columns))
    data = normalize_columns(data)
    logger.debug("Normalized columns: %s", list(data.columns))

    if not {"generation", "pr"}.issubset(data.columns):
        raise ValueError("Input data must contain 'generation' and 'pr' for portfolio overview.")

    fig = px.scatter(
        data,
        x="generation",
        y="pr",
        color="plant_short_name",
        title="Portfolio Overview: Generation vs PR",
        hover_data=["plant_long_name"] if "plant_long_name" in data.columns else None
    )
    dashboard = vzm.Dashboard(
        title="Portfolio Overview",
        pages=[vzm.Page(title="Portfolio", components=[vzm.Graph(figure=fig)])]
    )
    logger.info("Portfolio overview dashboard built successfully.")
    return json.loads(dashboard.model_dump_json(indent=2))
