def send_csv_file(csv_file_path, mobile_number, log_file="sent_csv_log.txt"):
    """
    Sends the CSV file to the given mobile number and logs the details.

    Args:
        csv_file_path (str): Path to the CSV file.
        mobile_number (str): Recipient's mobile number.
        log_file (str): File where logs will be stored.
    """
    try:
        # Append details to the log file
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(f"Mobile: {mobile_number}, CSV Path: {csv_file_path}\n")

        # Your actual logic to send the CSV file (stubbed for now)
        return {"Success": f"Successfully sent the csv file '{csv_file_path}' to {mobile_number}."}
    
    except Exception as e:
        return {"Error": str(e)}
