Mobile: 7259922398, CSV Path: D:\<PERSON><PERSON><PERSON><PERSON><PERSON>\MCP\MCP-VIZRO\exports\IN.INTE.KIDS_DAILY_GENERATION_20250902_113719.csv
Mobile: 7259922398, CSV Path: D:\<PERSON><PERSON><PERSON><PERSON><PERSON>\MCP\MCP-VIZRO\exports\IN.INTE.KIDS_DAILY_GENERATION_20250902_113820.csv
Mobile: 8867855303, Results: [{'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. <PERSON><PERSON><PERSON>', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755'}, {'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Solar', 'capacity': '3', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. <PERSON><PERSON><PERSON>', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755'}]
Mobile: 8867855303, Results: [{'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Irfan', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755'}, {'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Solar', 'capacity': '3', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Irfan', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755'}]
Mobile: 8867855303, Results: [{'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Irfan', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755'}, {'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Solar', 'capacity': '3', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Irfan', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755'}]
Mobile: 8867855303, Results: [{'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Irfan', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755'}, {'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Solar', 'capacity': '3', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Irfan', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755'}]
Mobile: 8867855303, Results: [{'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T02"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Irfan', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}, {'plant_id': 'IN.INTE.ANS1', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Solar', 'capacity': '3', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Irfan', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 8867855303, Results: [{'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T02"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Irfan', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}, {'plant_id': 'IN.INTE.ANS1', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Solar', 'capacity': '3', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Irfan', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9740015535, Results: [{'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T02"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}, {'plant_id': 'IN.INTE.ANS1', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Solar', 'capacity': '3', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9740015535, Results: [{'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T02"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}, {'plant_id': 'IN.INTE.ANS1', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Solar', 'capacity': '3', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9740015535, Results: [{'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T02"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}, {'plant_id': 'IN.INTE.ANS1', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Solar', 'capacity': '3', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9740015535, Results: [{'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T02"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}, {'plant_id': 'IN.INTE.ANS1', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Solar', 'capacity': '3', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9740015535, Results: [{'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T02"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}, {'plant_id': 'IN.INTE.ANS1', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Solar', 'capacity': '3', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9740015535, Results: [{'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T02"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}, {'plant_id': 'IN.INTE.ANS1', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Solar', 'capacity': '3', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9740015535, Results: [{'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T02"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}, {'plant_id': 'IN.INTE.ANS1', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Solar', 'capacity': '3', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9740015535, Results: [{'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T02"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}, {'plant_id': 'IN.INTE.ANS1', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Solar', 'capacity': '3', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9740015535, Results: [{'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T02"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}, {'plant_id': 'IN.INTE.ANS1', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Solar', 'capacity': '3', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9740015535, Results: [{'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T02"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}, {'plant_id': 'IN.INTE.ANS1', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Solar', 'capacity': '3', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9740015535, Results: [{'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T02"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}, {'plant_id': 'IN.INTE.ANS1', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Solar', 'capacity': '3', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9740015535, Results: [{'plant_id': 'IN.INTE.ANSP', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T02"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}, {'plant_id': 'IN.INTE.ANS1', 'client_name': 'ANS Paper Mill Pvt Ltd', 'type': 'Solar', 'capacity': '3', 'combined': '', 'turbine_metadata': '', 'contact_person': 'Mr. Afsar Pasha', 'contact_number': '8867855303, 9740015535, 9740015538', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9739688666, Results: [{'plant_id': 'IN.INTE.SPFL', 'client_name': 'Sipani Fibre Limited', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T01"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n},\n{\n  ""Loc No"": ""T02"", \n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Hanumantha', 'contact_number': '9739688666, 99020 57123, 9739557123, 9845057123, 9739057123', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9739688666, Results: [{'plant_id': 'IN.INTE.SPFL', 'client_name': 'Sipani Fibre Limited', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T01"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n},\n{\n  ""Loc No"": ""T02"", \n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Hanumantha', 'contact_number': '9739688666, 99020 57123, 9739557123, 9845057123, 9739057123', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9739688666, Results: [{'plant_id': 'IN.INTE.SPFL', 'client_name': 'Sipani Fibre Limited', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T01"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n},\n{\n  ""Loc No"": ""T02"", \n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Hanumantha', 'contact_number': '9739688666, 99020 57123, 9739557123, 9845057123, 9739057123', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9739688666, Results: [{'plant_id': 'IN.INTE.SPFL', 'client_name': 'Sipani Fibre Limited', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T01"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n},\n{\n  ""Loc No"": ""T02"", \n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Hanumantha', 'contact_number': '9739688666, 99020 57123, 9739557123, 9845057123, 9739057123', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9739688666, Results: [{'plant_id': 'IN.INTE.SPFL', 'client_name': 'Sipani Fibre Limited', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T01"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n},\n{\n  ""Loc No"": ""T02"", \n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Hanumantha', 'contact_number': '9739688666, 99020 57123, 9739557123, 9845057123, 9739057123', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9739688666, Results: [{'plant_id': 'IN.INTE.SPFL', 'client_name': 'Sipani Fibre Limited', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T01"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n},\n{\n  ""Loc No"": ""T02"", \n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Hanumantha', 'contact_number': '9739688666, 99020 57123, 9739557123, 9845057123, 9739057123', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9739688666, Results: [{'plant_id': 'IN.INTE.SPFL', 'client_name': 'Sipani Fibre Limited', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T01"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n},\n{\n  ""Loc No"": ""T02"", \n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Hanumantha', 'contact_number': '9739688666, 99020 57123, 9739557123, 9845057123, 9739057123', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9739688666, Results: [{'plant_id': 'IN.INTE.SPFL', 'client_name': 'Sipani Fibre Limited', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T01"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n},\n{\n  ""Loc No"": ""T02"", \n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Hanumantha', 'contact_number': '9739688666, 99020 57123, 9739557123, 9845057123, 9739057123', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9739688666, Results: [{'plant_id': 'IN.INTE.SPFL', 'client_name': 'Sipani Fibre Limited', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T01"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n},\n{\n  ""Loc No"": ""T02"", \n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Hanumantha', 'contact_number': '9739688666, 99020 57123, 9739557123, 9845057123, 9739057123', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9739688666, Results: [{'plant_id': 'IN.INTE.SPFL', 'client_name': 'Sipani Fibre Limited', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T01"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n},\n{\n  ""Loc No"": ""T02"", \n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Hanumantha', 'contact_number': '9739688666, 99020 57123, 9739557123, 9845057123, 9739057123', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9739688666, Results: [{'plant_id': 'IN.INTE.SPFL', 'client_name': 'Sipani Fibre Limited', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T01"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n},\n{\n  ""Loc No"": ""T02"", \n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Hanumantha', 'contact_number': '9739688666, 99020 57123, 9739557123, 9845057123, 9739057123', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
Mobile: 9739688666, Results: [{'plant_id': 'IN.INTE.SPFL', 'client_name': 'Sipani Fibre Limited', 'type': 'Wind', 'capacity': '2.7', 'combined': '', 'turbine_metadata': '[{\n  ""Loc No"": ""T01"",\n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n},\n{\n  ""Loc No"": ""T02"", \n  ""Avg Wind Speed"": 0,\n  ""Daily Generation (KWh)"": 0\n}]', 'contact_person': 'Mr. Hanumantha', 'contact_number': '9739688666, 99020 57123, 9739557123, 9845057123, 9739057123', 'test_number': '8857944602, 9960800576, 9567578391, 8866383755, 9392709889, 8143190194, 8904545164'}]
