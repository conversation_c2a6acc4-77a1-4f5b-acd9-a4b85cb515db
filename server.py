import os
import re
import json
import pandas as pd
from datetime import datetime
from typing import Any, List, Dict, Optional
from setup_db import engine
from sqlalchemy import text
from mcp.server.fastmcp import FastMCP
import logging
from datetime import date, timedelta



# =========================
# Configuration & Constants
# =========================

# Environment variables for configuration

ROOT_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
PLANT_PREFIX = "IN.INTE."
CUSTOMER_CSV = "customer_data - Sheet1.csv"



# =========================
# Logging Setup
# =========================

logger = logging.getLogger("solar_mcp")
LOG_LEVEL = os.getenv("MCP_LOG_LEVEL", "INFO").upper()

# Console handler
console_handler = logging.StreamHandler()
formatter = logging.Formatter("%(asctime)s %(levelname)s [%(name)s] %(message)s")
console_handler.setFormatter(formatter)

# File handler
log_file = os.path.join(ROOT_DIR, "logs", "solar_mcp.log")
os.makedirs(os.path.dirname(log_file), exist_ok=True)
file_handler = logging.FileHandler(log_file, mode="a", encoding="utf-8")
file_handler.setFormatter(formatter)

# Attach handlers if not already attached
if not logger.hasHandlers():
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
logger.setLevel(LOG_LEVEL)

# =========================
# FastMCP Server Init
# =========================

mcp = FastMCP("mcp_dgr_bot")

# =========================
# Initialize Prescinto integration
# =========================

INTEGRATION_SERVER = "IN"
INTEGRATION_TOKEN  = os.getenv("PRESCINTO_TOKEN")

from integration_utilities import PrescintoIntegrationUtilities
integration = PrescintoIntegrationUtilities(server=INTEGRATION_SERVER, token=INTEGRATION_TOKEN)



# --- Utility ---
# =========================
#  Helper functions
# =========================

def normalize_number(number: str) -> str:
    """Remove all non-digits from a phone number string."""
    return re.sub(r"\D", "", str(number).strip()) if number else ""



def _extract_plant_name(sql: str) -> str:
    """Extract plant_short_name from SQL query if present."""
    match = re.search(r"plant_short_name\s*=\s*'([^']+)'", sql, re.IGNORECASE)
    return match.group(1) if match else None


def _extract_metric(sql: str) -> str:
    """
    Extract a representative metric/column from SELECT clause.
    Skips common identifiers like plant_short_name, plant_long_name, and date.
    """
    select_match = re.search(r"SELECT\s+(.*?)\s+FROM", sql, re.IGNORECASE | re.DOTALL)
    if not select_match:
        return None

    select_fields = select_match.group(1)
    fields = [f.strip() for f in re.split(r",|\n", select_fields) if f.strip()]

    for f in fields:
        lower_f = f.lower()
        if any(x in lower_f for x in ["plant_short_name", "plant_long_name", "date"]):
            continue

        alias_match = re.search(r"AS\s+([A-Z0-9_]+)", f, re.IGNORECASE)
        if alias_match:
            return alias_match.group(1)

        # Fall back to first token (cleaned up)
        return re.sub(r"[^a-zA-Z0-9]", "", f.split()[0])

    return None



def _prepare_dataframe(json_input: str, value_column: str) -> pd.DataFrame:
    """
    Convert JSON string input into a cleaned pandas DataFrame.
    Ensures 'date' is converted from epoch ms to datetime.
    """
    records = json.loads(json_input)
    df = pd.DataFrame(records)

    # Convert epoch ms → datetime
    df["date"] = pd.to_datetime(df["date"], unit="ms")

    # Standardize column names
    df = df.rename(columns={
        "plant_long_name": "plant_name",
        value_column: "value"
    })

    return df[["date", "plant_name", "value"]]


def _save_plot(fig, prefix: str) -> str:
    """Save plot to EXPORT_DIR and return path."""
    os.makedirs(PLOT_EXPORT_DIR, exist_ok=True)
    filename = os.path.join(PLOT_EXPORT_DIR, f"{prefix}_{int(time.time())}.png")
    fig.savefig(filename, dpi=150, bbox_inches="tight")
    plt.close(fig)
    return filename


def _validate_date_str(date_str: str) -> str:
    if re.match(r"^\d{4}-\d{2}-\d{2}$", date_str):
        return date_str
    raise ValueError(f"Invalid date format: {date_str}")



# =========================
# MCP Tools
# =========================




@mcp.tool(
    name="get_metadata_by_contact_number",
    description=(
        "Retrieves detailed metadata for renewable energy plants by searching the customer CSV for records where the provided contact number matches the 'Contact Number' fields. "
        "Returns a list of all associated plants, including plant ID, client name, type, capacity, turbine metadata, and the corresponding contact person. "
        "Supports multiple matches and accurately maps contact persons to their respective contact numbers."
    )
)

def get_metadata_by_contact_number(input_number: str) -> List[Dict[str, Any]]:
    """
    Retrieves comprehensive metadata for renewable energy plants by searching the customer CSV for records where the provided contact number matches entries in the 'Contact Number' or 'Test Number' columns.

    Args:
        input_number (str): The contact number to search for. Can include spaces, dashes, or other non-digit characters.

    Returns:
        List[Dict[str, Any]]: A list of dictionaries, each containing metadata for a plant associated with the input number.
        Each dictionary includes:
            - 'plant_id' (str): The plant's unique identifier.
            - 'client_name' (str): The name of the customer/client.
            - 'type' (str): The type of plant (e.g., 'solar', 'wind').
            - 'capacity' (str): The plant's capacity in MW.
            - 'combined' (str): Combined plant information, if available.
            - 'turbine_metadata' (str): Turbine metadata, if available.
            - 'contact_person' (str): The mapped contact person for the matched number, or a default if not found.
            - 'contact_number' (str): The raw contact number(s) from the CSV.
            - 'test_number' (str): The raw test number(s) from the CSV.

    Example:
        >>> get_metadata_by_contact_number("9876543210")
        [
            {
                "plant_id": "IN.INTE.KIDS",
                "client_name": "ABC Energy",
                "type": "solar",
                "capacity": "5",
                "combined": "",
                "turbine_metadata": "",
                "contact_person": "John Doe",
                "contact_number": "9876543210",
                "test_number": ""
            },
            {
                "plant_id": "IN.INTE.ANS",
                "client_name": "ABC Energy",
                "type": "wind",
                "capacity": "3.7",
                "combined": "",
                "turbine_metadata": "",
                "contact_person": "John Doe",
                "contact_number": "9876543210",
                "test_number": ""
            }
        ]

    Notes:
        - Handles multiple matches and ensures contact person mapping if found in 'Contact Number'.
        - If the input number is found only in 'Test Number', 'contact_person' is set to "Test Number Contact".
        - Returns an empty list if no matches are found.

    """
    file_path = CUSTOMER_CSV

    # Normalize input number
    input_number = normalize_number(input_number)

    # Load CSV
    df = pd.read_csv(file_path, dtype=str).fillna("")

    results: List[Dict[str, Any]] = []

    for _, row in df.iterrows():
        contact_numbers_raw = str(row.get("Contact Number", ""))
        test_numbers_raw = str(row.get("Test Number", ""))
        contact_persons_raw = str(row.get("Contact Person", ""))

        # Normalize and split contact/test numbers
        contact_numbers_list = [normalize_number(num) for num in contact_numbers_raw.split(",") if normalize_number(num)]
        test_numbers_list = [normalize_number(num) for num in test_numbers_raw.split(",") if normalize_number(num)]
        contact_persons_list = [name.strip() for name in contact_persons_raw.split(",") if name.strip()]

        all_numbers = contact_numbers_list + test_numbers_list
        if input_number in all_numbers:
            # Map contact number to corresponding contact person
            if input_number in contact_numbers_list:
                idx = contact_numbers_list.index(input_number)
                matched_person = (
                    contact_persons_list[idx]
                    if idx < len(contact_persons_list)
                    else (contact_persons_list[-1] if contact_persons_list else "")
                )
            else:
                matched_person = "Test Number Contact"

       

            result = {
                "plant_id": str(row.get("Plant id", "")).strip(),
                "client_name": str(row.get("Customer Name", "")).strip(),
                "type": str(row.get("Type", "")).strip(),
                "capacity": str(row.get("Capacity ( MW)", "")).strip(),
                "combined": str(row.get("Combined", "")).strip(),
                "turbine_metadata": str(row.get("Turbine Metadata", "")).strip(),
                "contact_person": matched_person,
                "contact_number": contact_numbers_raw.strip(),
                "test_number": test_numbers_raw.strip(),
            }
            results.append(result)
    log_path = "sent_csv_log.txt"

    with open(log_path, "a", encoding="utf-8") as f:
            f.write(f"Mobile: {input_number}, Results: {results}\n")

    if not results:
        return []


    return results


@mcp.tool(
    name="get_plant_type",
    description=("Returns 'solar' or 'wind' for a given plant_id from a CSV. "
    "plant_id always start from IN.INTE."
)
)
def get_plant_type(plant_id: str) -> str:
    """
    Returns the type of plant ('solar' or 'wind') for a given plant_id from the customer CSV.

    Args:
        plant_id (str): The plant ID to look up.

    Returns:
        str: The type of the plant. Either 'solar' or 'wind'.

    Example:
        >>> get_plant_type("IN.INTE.KIDS")
        'solar'

    Raises:
        FileNotFoundError: If the customer CSV file is not found.
        KeyError: If the 'plant id' column is missing in the CSV.
        ValueError: If the plant_id is not found in the CSV.
    """
    if not os.path.isfile(CUSTOMER_CSV):
        raise FileNotFoundError(f"CSV not found at {CUSTOMER_CSV}")
    df = pd.read_csv(CUSTOMER_CSV)
    df.columns = df.columns.str.strip().str.lower()
    if 'plant id' not in df.columns:
        raise KeyError("Column 'plant id' missing in CSV")
    match = df[df['plant id'] == plant_id]
    if match.empty:
        raise ValueError(f"Plant ID '{plant_id}' not found")
    return match['type'].iloc[0]




@mcp.tool(
    name="get_generation_metrics_solar",
    description=(
        "Generate SQL queries to fetch solar plant generation metrics from the DGR database "
        "using the 'edit_generation' column. Supported metric types include: "
        "- 'today': Daily generation for the current date "
        "- 'yesterday': Daily generation for the previous date "
        "- 'monthly_total': Sum of generation from the first day of the current month up to today "
        "- 'monthly_average': Average daily generation from the first day of the current month up to today "
        "- 'highest_day_month': Day with the highest generation in the current month "
        "- 'lowest_day_month': Day with the lowest generation in the current month "
        "- 'ytd_total': Year-to-date total generation (Jan 1st of this year up to today) "
        "- 'lifetime_total': Total generation across all available records "
        "- 'trend': Daily generation trend for the last N days (requires 'days' parameter). "
        "- 'last_n_days_average': Average daily generation for the last N days (requires 'days' parameter)."
    )
)

def get_generation_metrics_solar(plant_id: str, metric_type: str, days: int = None) -> str:
    """
    Generate SQL queries to fetch solar plant generation metrics from the DGR database.

    Args:
        plant_id (str): Short name of the solar plant (e.g., 'IN.INTE.KIDS').
        "plant_id always start from IN.INTE.
        metric_type (str): Type of generation metric to fetch. Supported values:
            - 'today': Daily generation for the current date
            - 'yesterday': Daily generation for the previous date
            - 'monthly_total': Sum of generation from the first day of the current month up to today
            - 'monthly_average': Average daily generation from the first day of the current month up to today
            - 'highest_day_month': Day with the highest generation in the current month
            - 'lowest_day_month': Day with the lowest generation in the current month
            - 'ytd_total': Year-to-date total generation (Jan 1st of this year up to today)
            - 'lifetime_total': Total generation across all available records
            - 'trend': Daily generation trend for the last N days (requires 'days' parameter)
            - 'last_n_days_average': Average daily generation for the last N days (requires 'days' parameter)
        days (int, optional): Number of days for 'trend' or 'last_n_days_average' queries.

    Returns:
        str: SQL query string.

    Raises:
        ValueError: If an invalid metric_type is provided or 'days' is missing for 'trend' or 'last_n_days_average'.
    """
    today = date.today()
    yesterday = today - timedelta(days=1)
    first_day_of_month = today.replace(day=1)

    if metric_type == "today":
        sql = f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
              AND date = '{today}';
        """
    elif metric_type == "yesterday":
        sql = f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
              AND date = '{yesterday}';
        """
    elif metric_type == "monthly_total":
        first_day_of_month = today.replace(day=1)
        sql = f"""
            SELECT plant_long_name, SUM(edit_generation) AS MONTHLY_GENERATION
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
            AND date >= '{first_day_of_month}'
            AND date <= '{today}';
        """
    elif metric_type == "ytd_total":
        sql = f"""
            SELECT plant_long_name, SUM(edit_generation) AS YTD_GENERATION
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
              AND EXTRACT(YEAR FROM date) = {today.year};
        """
    elif metric_type == "lifetime_total":
        sql = f"""
            SELECT plant_long_name, SUM(edit_generation) AS LIFETIME_GENERATION
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}';
        """
    elif metric_type == "monthly_average":
        first_day_of_month = today.replace(day=1)
        sql = f"""
            SELECT plant_long_name, AVG(edit_generation) AS AVG_DAILY_GENERATION_THIS_MONTH
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
            AND date >= '{first_day_of_month}'
            AND date <= '{today}';
        """
    elif metric_type == "highest_day_month":
        sql = f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
              AND EXTRACT(MONTH FROM date) = {today.month}
              AND EXTRACT(YEAR FROM date) = {today.year}
            ORDER BY edit_generation DESC
            LIMIT 1;
        """
    elif metric_type == "lowest_day_month":
        sql = f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
              AND EXTRACT(MONTH FROM date) = {today.month}
              AND EXTRACT(YEAR FROM date) = {today.year}
            ORDER BY edit_generation ASC
            LIMIT 1;
        """
    elif metric_type == "trend" and days:
        start_date = today - timedelta(days=days)
        sql = f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
              AND date >= '{start_date}'
            ORDER BY date ASC;
        """
    elif metric_type == "last_n_days_average" and days:
        start_date = today - timedelta(days=days)
        sql = f"""
            SELECT plant_long_name, AVG(edit_generation) AS AVG_GENERATION_LAST_{days}_DAYS
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
              AND date >= '{start_date}'
              AND date <= '{today}';
        """
    else:
        raise ValueError("Invalid metric_type or missing days param for trend/last_n_days_average")
    
    return sql.strip()




@mcp.tool(
    name="get_pr_metrics_solar",
    description=(
        "Generate SQL queries to fetch solar plant Performance Ratio (PR%) metrics from the DGR database "
        "plant_id always start from IN.INTE."
        "using the 'pr' column. Supported metric types include: "
        "- 'today': Average PR% for the current date "
        "- 'yesterday': Average PR% for the previous date "
        "- 'monthly_average': Average PR% from the first day of the current month up to today "
        "- 'highest_day_month': Day with the highest average PR% in the current month "
        "- 'lowest_day_month': Day with the lowest average PR% in the current month "
        "- 'ytd_average': Year-to-date average PR% (Jan 1st of this year up to today) "
        "- 'lifetime_average': Average PR% across all available records "
        "- 'trend': Daily average PR% trend for the last N days (requires 'days' parameter). "
        "- 'last_n_days_average': Average PR% for the last N days (requires 'days' parameter)."
    )
)

def get_pr_metrics_solar(plant_id: str, metric_type: str, days: int = None) -> str:
    """
    Generate SQL queries to fetch solar plant Performance Ratio (PR%) metrics from the DGR database.

    Args:
        plant_id (str): Short name of the solar plant (e.g., 'IN.INTE.KIDS').
        metric_type (str): Type of PR metric to fetch. Supported values:
            - 'today': Average PR% for the current date
            - 'yesterday': Average PR% for the previous date
            - 'monthly_average': Average PR% from the first day of the current month up to today
            - 'highest_day_month': Day with the highest average PR% in the current month
            - 'lowest_day_month': Day with the lowest average PR% in the current month
            - 'ytd_average': Year-to-date average PR% (Jan 1st of this year up to today)
            - 'lifetime_average': Average PR% across all available records
            - 'trend': Daily average PR% trend for the last N days (requires 'days' parameter)
            - 'compare_months': Compare average PR% of this month vs last month
        days (int, optional): Number of days for 'trend' queries.

    Returns:
        str: SQL query string.

    Raises:
        ValueError: If an invalid metric_type is provided or 'days' is missing for 'trend'.
    """
    today = date.today()
    yesterday = today - timedelta(days=1)
    first_day_of_month = today.replace(day=1)
    
    if metric_type == "today":
        return f"""
        SELECT plant_long_name, date, edit_pr as PR_PERCENT
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
          AND date = '{today}';
        """

    elif metric_type == "yesterday":
        return f"""
        SELECT plant_long_name, date, pr as PR_PERCENT
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
          AND date = '{yesterday}';
        """

    elif metric_type == "monthly_average":
        return f"""
        SELECT plant_long_name, AVG(edit_pr) as MONTHLY_AVG_PR_PERCENT
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
          AND date BETWEEN '{first_day_of_month}' AND '{today}';
        """

    elif metric_type == "lifetime_average":
        return f"""
        SELECT plant_long_name, AVG(edit_pr) as LIFETIME_AVG_PR_PERCENT
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}';
        """

    elif metric_type == "trend":
        if not days:
            days = 7
        start_date = today - timedelta(days=days)
        return f"""
        SELECT plant_long_name, date, edit_pr as PR_PERCENT
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
          AND date BETWEEN '{start_date}' AND '{today}'
        ORDER BY date;
        """

    elif metric_type == "last_n_days_average" and days:
        start_date = today - timedelta(days=days)
        return f"""
        SELECT plant_long_name, AVG(edit_pr) AS AVG_PR_LAST_{days}_DAYS
        FROM dgr_solar_db
        WHERE plant_short_name = '{plant_id}'
          AND date BETWEEN '{start_date}' AND '{today}';
        """

    elif metric_type == "compare_months":
        return f"""
        WITH monthly_pr AS (
            SELECT
                DATE_TRUNC('month', date) as month,
                AVG(edit_pr) as avg_pr
            FROM dgr_solar_db
            WHERE plant_short_name = '{plant_id}'
            GROUP BY DATE_TRUNC('month', date)
        )
        SELECT *
        FROM edit_monthly_pr
        WHERE month IN (
            DATE_TRUNC('month', '{today}'),
            DATE_TRUNC('month', '{today}' - INTERVAL '1 month')
        )
        ORDER BY month;
        """

    else:
        raise ValueError("Invalid metric_type or missing days param for trend")
    


@mcp.tool(
    name="get_poa_metrics_solar",
    description=(
        "Generate SQL queries to fetch solar plant Plane of Array (POA, irradiance) metrics "
        "plant_id always start from IN.INTE."
        "from the DGR database using the 'poa' column. Supported metric types include: "
        "- 'today': POA for the current date "
        "- 'yesterday': POA for the previous date "
        "- 'monthly_average': Average daily POA from the first day of the current month up to today "
        "- 'highest_day_month': Day with the highest POA in the current month "
        "- 'lowest_day_month': Day with the lowest POA in the current month "
        "- 'daily_average_month': Daily average POA values across the current month "
        "- 'trend': Daily POA trend for the last N days (requires 'days' parameter) "
        "- 'last_n_days_average': Average daily POA for the last N days (requires 'days' parameter). "
        "- 'compare_last_month': Comparison of average POA for the current month vs the previous month."
    )
)
def get_poa_metrics_solar(plant_id: str, metric_type: str, days: int = None) -> str:
    """
    Generate SQL queries to fetch POA (Plane of Array irradiance) metrics for a solar plant.

    Args:
        plant_id (str): Short name of the solar plant (e.g., 'IN.INTE.KIDS').
        metric_type (str): Type of POA metric to fetch. Supported values:
            - 'today': POA for the current date
            - 'yesterday': POA for the previous date
            - 'monthly_average': Average daily POA from the first day of the current month up to today
            - 'highest_day_month': Day with the highest POA in the current month
            - 'lowest_day_month': Day with the lowest POA in the current month
            - 'daily_average_month': Daily average POA values across the current month
            - 'trend': Daily POA trend for the last N days (requires 'days' parameter)
            - 'compare_last_month': Comparison of average POA for the current month vs the previous month
        days (int, optional): Number of days for 'trend' queries.

    Returns:
        str: SQL query string.

    Raises:
        ValueError: If an invalid metric_type is provided or 'days' is missing for 'trend'.
    """
    base_query = "SELECT plant_long_name, date, poa FROM dgr_solar_db WHERE plant_short_name = '{plant_name}'"

    if metric_type == "today":
        return f"{base_query} AND date = CURRENT_DATE;"

    elif metric_type == "yesterday":
        return f"{base_query} AND date = CURRENT_DATE - INTERVAL '1 day';"

    elif metric_type == "monthly_average":
        return (
            f"SELECT plant_long_name, AVG(poa) AS monthly_average_poa "
            f"FROM dgr_solar_db "
            f"WHERE plant_short_name = '{plant_id}' "
            f"AND DATE_TRUNC('month', date) = DATE_TRUNC('month', CURRENT_DATE);"
        )

    elif metric_type == "highest_day_month":
        return (
            f"SELECT plant_long_name, date, poa FROM dgr_solar_db "
            f"WHERE plant_short_name = '{plant_id}' "
            f"AND DATE_TRUNC('month', date) = DATE_TRUNC('month', CURRENT_DATE) "
            f"ORDER BY poa DESC LIMIT 1;"
        )

    elif metric_type == "lowest_day_month":
        return (
            f"SELECT plant_long_name, date, poa FROM dgr_solar_db "
            f"WHERE plant_short_name = '{plant_id}' "
            f"AND DATE_TRUNC('month', date) = DATE_TRUNC('month', CURRENT_DATE) "
            f"ORDER BY poa ASC LIMIT 1;"
        )

    elif metric_type == "daily_average_month":
        return (
            f"SELECT plant_long_name, date, AVG(poa) AS daily_average_poa "
            f"FROM dgr_solar_db "
            f"WHERE plant_short_name = '{plant_id}' "
            f"AND DATE_TRUNC('month', date) = DATE_TRUNC('month', CURRENT_DATE) "
            f"GROUP BY plant_long_name, date ORDER BY date;"
        )

    elif metric_type == "trend":
        if not days:
            raise ValueError("The 'days' parameter is required for the 'trend' metric_type.")
        return (
            f"SELECT plant_long_name, date, poa FROM dgr_solar_db "
            f"WHERE plant_short_name = '{plant_id}' "
            f"AND date >= CURRENT_DATE - INTERVAL '{days} days' "
            f"ORDER BY date;"
        )

    elif metric_type == "last_n_days_average" and days:
        return (
            f"SELECT plant_long_name, AVG(poa) AS AVG_POA_LAST_{days}_DAYS "
            f"FROM dgr_solar_db "
            f"WHERE plant_short_name = '{plant_id}' "
            f"AND date >= CURRENT_DATE - INTERVAL '{days} days' "
            f"AND date <= CURRENT_DATE;"
        )

    elif metric_type == "compare_last_month":
        return (
            f"WITH current_month AS ( "
            f"    SELECT AVG(poa) AS avg_poa "
            f"    FROM dgr_solar_db "
            f"    WHERE plant_short_name = '{plant_id}' "
            f"    AND DATE_TRUNC('month', date) = DATE_TRUNC('month', CURRENT_DATE) "
            f"), last_month AS ( "
            f"    SELECT AVG(poa) AS avg_poa "
            f"    FROM dgr_solar_db "
            f"    WHERE plant_short_name = '{plant_id}' "
            f"    AND DATE_TRUNC('month', date) = DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month') "
            f") "
            f"SELECT cm.avg_poa AS current_month_poa, lm.avg_poa AS last_month_poa "
            f"FROM current_month cm, last_month lm;"
        )

    else:
        raise ValueError(f"Unsupported metric_type: {metric_type}")






# ===============================
# WIND GENERATION TOOL
# ===============================
@mcp.tool(
    name="get_generation_metrics_wind",
    description=(
        "Generate SQL queries to fetch wind plant generation metrics from the DGR database "
        "plant_id always start from IN.INTE."
        "using the 'edit_generation' column. Supported metric types include: "
        "- 'today': Daily generation for the current date "
        "- 'yesterday': Daily generation for the previous date "
        "- 'monthly_total': Sum of generation from the first day of the current month up to today "
        "- 'monthly_average': Average daily generation this month "
        "- 'highest_day_month': Day with the highest generation this month "
        "- 'lowest_day_month': Day with the lowest generation this month "
        "- 'ytd_total': Year-to-date total generation (Jan 1st up to today) "
        "- 'lifetime_total': Total generation across all available records "
        "- 'trend': Daily generation trend for the last N days (requires 'days' parameter). "
        "- 'last_n_days_average': Average daily generation for the last N days (requires 'days' parameter)."
    )
)
def get_generation_metrics_wind(plant_id: str, metric_type: str, days: int = None) -> str:
    """
    Generate SQL queries to fetch wind plant generation metrics from the DGR database.

    Args:
        plant_id (str): Short name of the wind plant (e.g., 'IN.INTE.KIDS').
        metric_type (str): Type of generation metric to fetch. Supported values:
            - 'today': Daily generation for the current date
            - 'yesterday': Daily generation for the previous date
            - 'monthly_total': Sum of generation from the first day of the current month up to today
            - 'monthly_average': Average daily generation this month
            - 'highest_day_month': Day with the highest generation this month
            - 'lowest_day_month': Day with the lowest generation this month
            - 'ytd_total': Year-to-date total generation (Jan 1st up to today)
            - 'lifetime_total': Total generation across all available records
            - 'trend': Daily generation trend for the last N days (requires 'days' parameter)
            - 'last_n_days_average': Average generation for the last N days (requires 'days' parameter)
        days (int, optional): Number of days for 'trend' or 'last_n_days_average' queries.

    Returns:
        str: SQL query string.

    Raises:
        ValueError: If an invalid metric_type is provided or 'days' is missing for 'trend'.
    """
    today = date.today()
    yesterday = today - timedelta(days=1)
    first_day_of_month = today.replace(day=1)

    if metric_type == "today":
        return f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date = '{today}';
        """

    elif metric_type == "yesterday":
        return f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date = '{yesterday}';
        """

    elif metric_type == "monthly_total":
        return f"""
            SELECT plant_short_name, plant_long_name, SUM(edit_generation) AS MONTHLY_GENERATION
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date BETWEEN '{first_day_of_month}' AND '{today}'
            GROUP BY plant_short_name, plant_long_name;
        """

    elif metric_type == "monthly_average":
        return f"""
            SELECT plant_short_name, plant_long_name, AVG(edit_generation) AS AVG_DAILY_GENERATION_THIS_MONTH
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date BETWEEN '{first_day_of_month}' AND '{today}'
            GROUP BY plant_short_name, plant_long_name;
        """

    elif metric_type == "highest_day_month":
        return f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND EXTRACT(MONTH FROM date) = {today.month}
              AND EXTRACT(YEAR FROM date) = {today.year}
            ORDER BY edit_generation DESC
            LIMIT 1;
        """

    elif metric_type == "lowest_day_month":
        return f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND EXTRACT(MONTH FROM date) = {today.month}
              AND EXTRACT(YEAR FROM date) = {today.year}
            ORDER BY edit_generation ASC
            LIMIT 1;
        """

    elif metric_type == "ytd_total":
        return f"""
            SELECT plant_short_name, plant_long_name, SUM(edit_generation) AS YTD_GENERATION
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND EXTRACT(YEAR FROM date) = {today.year}
            GROUP BY plant_short_name, plant_long_name;
        """

    elif metric_type == "lifetime_total":
        return f"""
            SELECT plant_short_name, plant_long_name, SUM(edit_generation) AS LIFETIME_GENERATION
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
            GROUP BY plant_short_name, plant_long_name;
        """

    elif metric_type == "trend" and days:
        start_date = today - timedelta(days=days)
        return f"""
            SELECT plant_long_name, date, edit_generation AS DAILY_GENERATION
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date >= '{start_date}'
            ORDER BY date ASC;
        """

    elif metric_type == "last_n_days_average" and days:
        start_date = today - timedelta(days=days)
        return f"""
            SELECT plant_short_name, plant_long_name, AVG(edit_generation) AS AVG_GENERATION_LAST_{days}_DAYS
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date BETWEEN '{start_date}' AND '{today}'
            GROUP BY plant_short_name, plant_long_name;
        """

    else:
        raise ValueError("Invalid metric_type or missing days param for trend/average")



# ===============================
# WIND SPEED TOOL
# ===============================
@mcp.tool(
    name="get_wind_speed_metrics_wind",
    description=(
        "Generate SQL queries to fetch wind plant Wind Speed metrics from the DGR database "
        "plant_id always start from IN.INTE"
        "using the 'edit_wind_speed' column. Supported metric types include: "
        "- 'today': Average wind speed for the current date "
        "- 'yesterday': Average wind speed for the previous date "
        "- 'monthly_average': Average wind speed from the first day of the current month up to today "
        "- 'highest_day_month': Day with the highest wind speed this month "
        "- 'lowest_day_month': Day with the lowest wind speed this month "
        "- 'lifetime_average': Lifetime average wind speed "
        "- 'trend': Daily wind speed trend for the last N days (requires 'days' parameter) "
        "- 'last_n_days_average': Average wind speed for the last N days (requires 'days' parameter). "
        "- 'compare_months': Compare average wind speed of this month vs last month."
    )
)
def get_wind_speed_metrics_wind(plant_id: str, metric_type: str, days: int = None) -> str:
    """
    Generate SQL queries to fetch wind plant wind speed metrics from the DGR database.

    Args:
        plant_id (str): Short name of the wind plant (e.g., 'IN.INTE.KIDS').
        metric_type (str): Type of wind speed metric to fetch. Supported values:
            - 'today': Average wind speed for the current date
            - 'yesterday': Average wind speed for the previous date
            - 'monthly_average': Average wind speed from the first day of the current month up to today
            - 'highest_day_month': Day with the highest wind speed this month
            - 'lowest_day_month': Day with the lowest wind speed this month
            - 'lifetime_average': Lifetime average wind speed
            - 'trend': Daily wind speed trend for the last N days (requires 'days' parameter)
            - 'compare_months': Compare average wind speed of this month vs last month
        days (int, optional): Number of days for 'trend' queries.

    Returns:
        str: SQL query string.

    Raises:
        ValueError: If an invalid metric_type is provided or 'days' is missing for 'trend'.
    """
    today = date.today()
    yesterday = today - timedelta(days=1)
    first_day_of_month = today.replace(day=1)

    if metric_type == "today":
        return f"""
            SELECT date, edit_wind_speed AS WIND_SPEED
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date = '{today}';
        """

    elif metric_type == "yesterday":
        return f"""
            SELECT date, edit_wind_speed AS WIND_SPEED
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date = '{yesterday}';
        """

    elif metric_type == "monthly_average":
        return f"""
            SELECT AVG(edit_wind_speed) AS MONTHLY_AVG_WIND_SPEED
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date BETWEEN '{first_day_of_month}' AND '{today}';
        """

    elif metric_type == "highest_day_month":
        return f"""
            SELECT date, edit_wind_speed AS WIND_SPEED
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND EXTRACT(MONTH FROM date) = {today.month}
              AND EXTRACT(YEAR FROM date) = {today.year}
            ORDER BY edit_wind_speed DESC
            LIMIT 1;
        """

    elif metric_type == "lowest_day_month":
        return f"""
            SELECT date, edit_wind_speed AS WIND_SPEED
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND EXTRACT(MONTH FROM date) = {today.month}
              AND EXTRACT(YEAR FROM date) = {today.year}
            ORDER BY edit_wind_speed ASC
            LIMIT 1;
        """

    elif metric_type == "lifetime_average":
        return f"""
            SELECT AVG(edit_wind_speed) AS LIFETIME_AVG_WIND_SPEED
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}';
        """

    elif metric_type == "trend" and days:
        start_date = today - timedelta(days=days)
        return f"""
            SELECT date, edit_wind_speed AS WIND_SPEED
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date >= '{start_date}'
            ORDER BY date ASC;
        """

    elif metric_type == "last_n_days_average" and days:
        start_date = today - timedelta(days=days)
        return f"""
            SELECT AVG(edit_wind_speed) AS AVG_WIND_SPEED_LAST_{days}_DAYS
            FROM dgr_wind_db
            WHERE plant_short_name = '{plant_id}'
              AND date >= '{start_date}'
              AND date <= '{today}';
        """

    elif metric_type == "compare_months":
        return f"""
            WITH monthly_ws AS (
                SELECT DATE_TRUNC('month', date) AS month,
                       AVG(edit_wind_speed) AS avg_ws
                FROM dgr_wind_db
                WHERE plant_short_name = '{plant_id}'
                GROUP BY DATE_TRUNC('month', date)
            )
            SELECT *
            FROM monthly_ws
            WHERE month IN (
                DATE_TRUNC('month', '{today}'),
                DATE_TRUNC('month', '{today}' - INTERVAL '1 month')
            )
            ORDER BY month;
        """

    else:
        raise ValueError("Invalid metric_type or missing days param for trend")



@mcp.tool(
    name="fetch_turbine_wise_metrics",
    description=(
        "Generate SQL queries to fetch turbine-wise wind speed and generation metrics "
        "the plant_id is always start with IN.INTE."
        "from the dgr_wind_db (edit_csv_report_data column) for a given wind plant.\n"
        "The column stores JSON arrays like: "
        "[{\"Loc No\":\"T01\",\"Avg Wind Speed\":\"8.02\",\"Daily Generation (kWh)\":\"37606\"}, ...].\n"
        "Both 'Avg Wind Speed' and 'Daily Generation (kWh)' are extracted together. "
        "Empty or missing values are treated as NULLs in aggregation.\n\n"
        "Supported metrics:\n"
        "- 'today': Wind speed + generation for the current date\n"
        "- 'yesterday': Wind speed + generation for the previous date\n"
        "- 'monthly_average': Average wind speed + generation this month\n"
        "- 'monthly_total': Total generation this month (wind speed averaged)\n"
        "- 'highest_day_month': Day with the highest generation this month (with wind speed)\n"
        "- 'lowest_day_month': Day with the lowest generation this month (with wind speed)\n"
        "- 'ytd_total': Year-to-date total generation (with avg wind speed)\n"
        "- 'lifetime_total': Lifetime generation + average wind speed\n"
        "- 'trend': Daily wind speed + generation trend for the last N days (requires 'days' parameter)\n"
        "- 'last_n_days_average': Average wind speed and generation per turbine for the last N days (requires 'days' parameter).\n"
        "- 'compare_months': Compare average wind speed + generation of this month vs last month\n"
        "- 'debug_raw': Raw expansion of JSON rows without aggregation (for debugging data ingestion)\n"
    )
)
def fetch_turbine_wise_metrics(
    plant_id: str,
    metric: str,
    days: int = None,
) -> str:
    """
    Generate MySQL SQL queries to fetch turbine-wise wind speed and generation metrics.

    Args:
        plant_id (str): Plant identifier (e.g., 'IN.INTE.KIDS').
        metric (str): today, yesterday, monthly_average, monthly_total,
                      highest_day_month, lowest_day_month, ytd_total,
                      lifetime_total, trend, compare_months, debug_raw
        days (int, optional): Number of days for 'trend' (default=7).

    Returns:
        str: SQL query string.
    """
    today = date.today()
    yesterday = today - timedelta(days=1)
    first_of_month = today.replace(day=1)
    first_of_year = today.replace(month=1, day=1)

    # JSON_TABLE for expansion
    json_table = """
        JSON_TABLE(
            d.edit_csv_report_data, '$[*]'
            COLUMNS(
                turbine_id VARCHAR(20) PATH '$."Loc No"',
                avg_ws DECIMAL(10,2) PATH '$."Avg Wind Speed"',
                gen_kwh DECIMAL(15,2) PATH '$."Daily Generation (kWh)"'
            )
        ) elem
    """

    if metric == "debug_raw":
        return f"""
            SELECT d.date, elem.turbine_id, elem.avg_ws, elem.gen_kwh
            FROM dgr_wind_db d,
                 {json_table}
            WHERE d.plant_short_name = '{plant_id}'
              AND d.date BETWEEN '{first_of_month}' AND '{today}'
            ORDER BY d.date, elem.turbine_id;
        """

    if metric == "today":
        return f"""
            SELECT d.date, elem.turbine_id,
                   AVG(elem.avg_ws) AS avg_wind_speed,
                   SUM(elem.gen_kwh) AS daily_generation
            FROM dgr_wind_db d,
                 {json_table}
            WHERE d.plant_short_name = '{plant_id}' AND d.date = '{today}'
            GROUP BY d.date, elem.turbine_id;
        """

    elif metric == "yesterday":
        return f"""
            SELECT d.date, elem.turbine_id,
                   AVG(elem.avg_ws) AS avg_wind_speed,
                   SUM(elem.gen_kwh) AS daily_generation
            FROM dgr_wind_db d,
                 {json_table}
            WHERE d.plant_short_name = '{plant_id}' AND d.date = '{yesterday}'
            GROUP BY d.date, elem.turbine_id;
        """

    elif metric == "monthly_average":
        return f"""
            SELECT elem.turbine_id,
                   AVG(elem.avg_ws) AS monthly_avg_wind_speed,
                   AVG(elem.gen_kwh) AS monthly_avg_generation
            FROM dgr_wind_db d,
                 {json_table}
            WHERE d.plant_short_name = '{plant_id}'
              AND d.date BETWEEN '{first_of_month}' AND '{today}'
            GROUP BY elem.turbine_id;
        """

    elif metric == "monthly_total":
        return f"""
            SELECT elem.turbine_id,
                   AVG(elem.avg_ws) AS monthly_avg_wind_speed,
                   SUM(elem.gen_kwh) AS monthly_total_generation
            FROM dgr_wind_db d,
                 {json_table}
            WHERE d.plant_short_name = '{plant_id}'
              AND d.date BETWEEN '{first_of_month}' AND '{today}'
            GROUP BY elem.turbine_id;
        """

    elif metric == "highest_day_month":
        return f"""
            WITH daily AS (
                SELECT d.date, elem.turbine_id,
                       AVG(elem.avg_ws) AS avg_wind_speed,
                       SUM(elem.gen_kwh) AS daily_generation
                FROM dgr_wind_db d,
                     {json_table}
                WHERE d.plant_short_name = '{plant_id}'
                  AND d.date BETWEEN '{first_of_month}' AND '{today}'
                GROUP BY d.date, elem.turbine_id
            )
            SELECT d1.*
            FROM daily d1
            WHERE d1.daily_generation = (
                SELECT MAX(d2.daily_generation) FROM daily d2
                WHERE d2.turbine_id = d1.turbine_id
            );
        """

    elif metric == "lowest_day_month":
        return f"""
            WITH daily AS (
                SELECT d.date, elem.turbine_id,
                       AVG(elem.avg_ws) AS avg_wind_speed,
                       SUM(elem.gen_kwh) AS daily_generation
                FROM dgr_wind_db d,
                     {json_table}
                WHERE d.plant_short_name = '{plant_id}'
                  AND d.date BETWEEN '{first_of_month}' AND '{today}'
                GROUP BY d.date, elem.turbine_id
            )
            SELECT d1.*
            FROM daily d1
            WHERE d1.daily_generation = (
                SELECT MIN(d2.daily_generation) FROM daily d2
                WHERE d2.turbine_id = d1.turbine_id
            );
        """

    elif metric == "ytd_total":
        return f"""
            SELECT elem.turbine_id,
                   AVG(elem.avg_ws) AS ytd_avg_wind_speed,
                   SUM(elem.gen_kwh) AS ytd_total_generation
            FROM dgr_wind_db d,
                 {json_table}
            WHERE d.plant_short_name = '{plant_id}'
              AND d.date BETWEEN '{first_of_year}' AND '{today}'
            GROUP BY elem.turbine_id;
        """

    elif metric == "lifetime_total":
        return f"""
            SELECT elem.turbine_id,
                   AVG(elem.avg_ws) AS lifetime_avg_wind_speed,
                   SUM(elem.gen_kwh) AS lifetime_total_generation
            FROM dgr_wind_db d,
                 {json_table}
            WHERE d.plant_short_name = '{plant_id}'
            GROUP BY elem.turbine_id;
        """

    elif metric == "trend":
        return f"""
            SELECT d.date, elem.turbine_id,
                   AVG(elem.avg_ws) AS avg_wind_speed,
                   SUM(elem.gen_kwh) AS daily_generation
            FROM dgr_wind_db d,
                 {json_table}
            WHERE d.plant_short_name = '{plant_id}'
              AND d.date BETWEEN '{today - timedelta(days=days or 7)}' AND '{today}'
            GROUP BY d.date, elem.turbine_id
            ORDER BY d.date, elem.turbine_id;
        """

    elif metric == "last_n_days_average" and days:
        start_date = today - timedelta(days=days)
        return f"""
            SELECT elem.turbine_id,
                   AVG(elem.avg_ws) AS avg_wind_speed_last_{days}_days,
                   AVG(elem.gen_kwh) AS avg_generation_last_{days}_days
            FROM dgr_wind_db d,
                 {json_table}
            WHERE d.plant_short_name = '{plant_id}'
              AND d.date >= '{start_date}'
              AND d.date <= '{today}'
            GROUP BY elem.turbine_id;
        """

    elif metric == "compare_months":
        last_month_end = first_of_month - timedelta(days=1)
        last_month_start = last_month_end.replace(day=1)

        return f"""
            WITH this_month AS (
                SELECT elem.turbine_id,
                       AVG(elem.avg_ws) AS avg_ws,
                       AVG(elem.gen_kwh) AS avg_gen
                FROM dgr_wind_db d,
                     {json_table}
                WHERE d.plant_short_name = '{plant_id}'
                  AND d.date BETWEEN '{first_of_month}' AND '{today}'
                GROUP BY elem.turbine_id
            ),
            last_month AS (
                SELECT elem.turbine_id,
                       AVG(elem.avg_ws) AS avg_ws,
                       AVG(elem.gen_kwh) AS avg_gen
                FROM dgr_wind_db d,
                     {json_table}
                WHERE d.plant_short_name = '{plant_id}'
                  AND d.date BETWEEN '{last_month_start}' AND '{last_month_end}'
                GROUP BY elem.turbine_id
            )
            SELECT COALESCE(t1.turbine_id, t2.turbine_id) AS turbine_id,
                   t1.avg_ws AS this_month_avg_ws,
                   t1.avg_gen AS this_month_avg_gen,
                   t2.avg_ws AS last_month_avg_ws,
                   t2.avg_gen AS last_month_avg_gen
            FROM this_month t1
            LEFT JOIN last_month t2 ON t1.turbine_id = t2.turbine_id;
        """

    else:
        raise ValueError(f"Invalid metric: {metric}")


@mcp.tool(
    name="fetch_data_from_db",
    description="Executes a raw SQL query against the database and returns results as JSON."
)
def fetch_data_from_db(sql_query: str) -> str:
    """
    Execute a raw SQL query against the database and return results as a JSON string.

    Args:
        sql_query (str): A valid SQL query string to execute against the database.

    Returns:
        str: Query results as a JSON string (list of records).

    Raises:
        RuntimeError: If the database query fails.
    """
    try:
        with engine.connect() as connection:
            df = pd.read_sql(text(sql_query), connection)
            # Convert DataFrame to JSON (records format keeps it list-of-dicts)
            return df.to_json(orient="records")
    except Exception as e:
        logger.error(f"Database query failed: {e}")
        raise RuntimeError(f"Database query failed: {e}")
    



EXPORT_DIR = os.path.join(ROOT_DIR, "exports")
from msg_sender import send_csv_file

@mcp.tool(
    name="create_csv_sent_file",
    description=(
        "Executes a SQL query, exports the result as a CSV under exports/, "
        "Only call this tool if the user explicitly asks to create a CSV file."
        "and sends it to the provided mobile number. "
        "When this tool completes successfully, it should NOT return the CSV path to the user. "
        "Instead, the assistant must reply to the customer with the exact sentence template below "
        "(substitute placeholders dynamically):\n\n"
        "above is the csv file asked about the {date_range_description} for your {plant_type}, {plant_long_name}. "
        "Do not alter capitalization, punctuation, or wording of this template except to replace "
        "{date_range_description}, {plant_type}, and {plant_long_name} with the values inferred from the SQL query result."
    ),
)

def create_csv_sent_file(sql_query: str, mobile_number: str) -> Dict[str, Any]:
    """
    Execute a SQL query, export the result as a CSV, and send it to a mobile number.

    Args:
        sql_query (str): SQL query string to execute.
        mobile_number (str): Recipient's mobile number.

    Returns:
        Dict[str, Any]: {"success": True, "message": "..."}
    """
    logger.info("create_csv_sent_file invoked with query: %s", sql_query)

    # --- Step 1: Fetch data from DB ---
    try:
        json_str = fetch_data_from_db(sql_query)
        records = json.loads(json_str)
        if not records:
            raise ValueError("Query returned no data.")
        df = pd.DataFrame(records)
    except Exception as e:
        logger.exception("Data fetch or parsing failed")
        return {"success": False, "message": f"Unable to fetch or parse results: {e}"}

    # --- Step 2: Normalize date/time columns ---
    for col in df.columns:
        if pd.api.types.is_integer_dtype(df[col]):
            if df[col].between(1e12, 2e13).any():
                try:
                    df[col] = pd.to_datetime(df[col], unit="ms").dt.strftime("%Y-%m-%d")
                except Exception:
                    pass  # leave as-is if conversion fails

    # --- Step 3: Generate file name ---
    plant = _extract_plant_name(sql_query) or "plant"
    metric = _extract_metric(sql_query) or "data"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{plant}_{metric}_{timestamp}.csv"
    file_path = os.path.join(EXPORT_DIR, filename)

    # --- Step 4: Save CSV ---
    try:
        df.to_csv(file_path, index=False)
        logger.info("CSV successfully written: %s", file_path)
    except Exception as e:
        logger.exception("CSV writing failed")
        return {"success": False, "message": f"Failed to write CSV: {e}"}

    # --- Step 5: Send CSV ---
    try:
        send_csv_file(file_path, mobile_number)
        logger.info("CSV sent to %s", mobile_number)
    except Exception as e:
        logger.exception("Failed sending CSV")
        return {"success": False, "message": f"Failed to send CSV: {e}"}

    return {"success": True, "message": "CSV file successfully sent."}



PLOT_EXPORT_DIR = os.path.join(ROOT_DIR, "export_plots")

import time
import matplotlib.pyplot as plt
import seaborn as sns




def _plot_metric_line(
    json_input: str,
    value_column: str,
    title: str,
    y_label: str,
    filename_prefix: str,
    reference_lines: list = None,
    legend_title: str = "Plant"
) -> dict:
    """
    Generic helper to create a line plot for plant metrics.
    Supports plotting for multiple plants (including hybrid/multi-type scenarios).
    If both solar and wind plants are present, the legend will indicate plant type.

    Args:
        json_input (str): JSON string of records. Should include 'plant_long_name' or 'plant_name', 'date', and the metric column.
        value_column (str): Column name in input for the y-axis value.
        title (str): Plot title.
        y_label (str): Y-axis label.
        filename_prefix (str): Prefix for saved plot filename.
        reference_lines (list, optional): List of dicts with keys 'y', 'color', 'linestyle', 'label'.
        legend_title (str): Title for the legend.

    Returns:
        dict: {"plot_path": ...}
    """
    # Prepare DataFrame and check for plant_type
    df = _prepare_dataframe(json_input, value_column)
    # Try to add plant_type if present in input
    try:
        records = json.loads(json_input)
        if records and isinstance(records, list) and "type" in records[0]:
            types = [rec.get("type", "") for rec in records]
            df["plant_type"] = types
        else:
            df["plant_type"] = ""
    except Exception:
        df["plant_type"] = ""

    # Compose plant label as "plant_name (type)" if multiple types present
    if "plant_type" in df.columns and df["plant_type"].nunique() > 1:
        df["plant_label"] = df["plant_name"] + " (" + df["plant_type"].str.capitalize() + ")"
        legend_col = "plant_label"
        legend_title = "Plant (Type)"
    else:
        legend_col = "plant_name"

    sns.set(style="whitegrid", context="talk")
    fig, ax = plt.subplots(figsize=(12, 6))
    # Robust: handle empty data
    if df.empty:
        ax.text(0.5, 0.5, "No data available", ha="center", va="center", fontsize=16, color="red", transform=ax.transAxes)
        ax.set_axis_off()
        path = _save_plot(fig, filename_prefix)
        return {"plot_path": path}

    sns.lineplot(data=df, x="date", y="value", hue=legend_col, marker="o", ax=ax)

    if reference_lines:
        for ref in reference_lines:
            ax.axhline(
                ref["y"],
                color=ref.get("color", "red"),
                linestyle=ref.get("linestyle", "--"),
                label=ref.get("label", None)
            )

    # Dynamic title: append "(Hybrid)" if both solar and wind present
    if "plant_type" in df.columns and df["plant_type"].nunique() > 1:
        plot_title = f"{title} (Hybrid)"
    else:
        plot_title = title

    ax.set_title(plot_title, fontsize=16, weight="bold")
    ax.set_xlabel("Date")
    ax.set_ylabel(y_label)
    plt.xticks(rotation=45)
    # Show legend if multiple plants or reference lines
    if df[legend_col].nunique() > 1 or reference_lines:
        ax.legend(title=legend_title, bbox_to_anchor=(1.05, 1), loc="upper left")
    else:
        if ax.legend_:
            ax.legend_.remove()

    path = _save_plot(fig, filename_prefix)
    return {"plot_path": path}

@mcp.tool(
    name="plot_generation_metrics",
    description="Create line plot of daily generation (kwh) for one or multiple plants."
)
def plot_generation_metrics(json_input: str) -> dict:
    return _plot_metric_line(
        json_input=json_input,
        value_column="DAILY_GENERATION",
        title="Daily Generation (MWh)",
        y_label="Generation (MWh)",
        filename_prefix="generation"
    )


@mcp.tool(
    name="plot_pr_metrics",
    description="Create line plot of daily PR% for solar plants."
)
def plot_pr_metrics(json_input: str) -> dict:
    return _plot_metric_line(
        json_input=json_input,
        value_column="PR",
        title="Daily Performance Ratio (PR%)",
        y_label="PR (%)",
        filename_prefix="pr",
        reference_lines=[
            {"y": 75, "color": "red", "linestyle": "--", "label": "PR Threshold (75%)"}
        ]
    )


@mcp.tool(
    name="plot_poa_metrics",
    description="Create line plot of daily POA irradiance (kWh/m²) for solar plants."
)
def plot_poa_metrics(json_input: str) -> dict:
    return _plot_metric_line(
        json_input=json_input,
        value_column="POA",
        title="Daily Plane of Array (POA)",
        y_label="POA (kWh/m²)",
        filename_prefix="poa"
    )


@mcp.tool(
    name="plot_wind_speed_metrics",
    description="Create line plot of daily average wind speed (m/s) for wind plants."
)
def plot_wind_speed_metrics(json_input: str) -> dict:
    return _plot_metric_line(
        json_input=json_input,
        value_column="WIND_SPEED",
        title="Daily Average Wind Speed",
        y_label="Wind Speed (m/s)",
        filename_prefix="wind_speed",
        reference_lines=[
            {"y": 3, "color": "green", "linestyle": "--", "label": "Cut-in Speed (3 m/s)"},
            {"y": 25, "color": "red", "linestyle": "--", "label": "Cut-out Speed (25 m/s)"}
        ]
    )





@mcp.tool(
    name="resolve_date",
    description="Converts a natural language date query into YYYY-MM-DD format."
)
def resolve_date(question: str) -> str:
    now = datetime.now()
    return f"""
You are an AI date resolver tool.

Based on the user's query, return the correct date in the format YYYY-MM-DD. 
Today's date is {now.strftime('%Y-%m-%d')}.

Rules:
- Only return the resolved date string (e.g., 2025-05-06).
- Do not include any text, explanations, or formatting around the date.
- If the query is relative (e.g., "yesterday", "this month", "last week"), compute accordingly.
- If ambiguous, resolve using best common interpretation.

Examples:
User: "What is yesterday's generation?"
Assistant: 2025-05-06

User: "What is this month's generation?"
Assistant: 2025-05-01

User: "Show data from last week"
Assistant: 2025-04-30

Now resolve for:
User: "{question}"
"""




@mcp.tool(
    name="get_alarm",
    description=(
        "Retrieves all inactive alarms for a specified plant within a given date range. "
        "Returns details including alarm name, controller name, message, severity, and state. "
        "This function aids in diagnosing plant issues such as breakdowns and maintenance requirements."
    )
)
def get_alarm_wind(
    plant_name: str,
    start_date: str,
    end_date: str
) -> List[Dict[str, str]]:
    """
    Fetches inactive alarm records for a specified plant between two dates,
    focusing on key details to assist in identifying and resolving plant issues.

    Args:
        plant_name (str): The name of the plant. Must start with the prefix defined in PLANT_PREFIX.
        start_date (str): Start date of the query range in 'YYYY-MM-DD' format.
        end_date (str): End date of the query range in 'YYYY-MM-DD' format.

    Returns:
        List[Dict[str, str]]: A list of dictionaries, each containing:
            - name: Alarm name (alarmname)
            - controller: Controller name (controllername)
            - message: Description of the alarm
            - severity: Severity level of the alarm
            - state: State of the alarm (expected to be "Inactive")

    Raises:
        ValueError: If the plant_name does not start with the required prefix,
                    or if start_date is after end_date,
                    or if date formats are incorrect.

    Example:
        get_alarm("PLANT_XYZ", "2025-05-01", "2025-05-31")
    """
    logger.info(f"Called get_alarm: {plant_name}, {start_date} to {end_date}")

    # Validate plant name
    if not plant_name.startswith(PLANT_PREFIX):
        raise ValueError(f"plant_name must start with '{PLANT_PREFIX}'")

    # Validate & parse dates
    start = _validate_date_str(start_date)
    end = _validate_date_str(end_date)
    if start > end:
        raise ValueError("start_date must be on or before end_date")

    # Define columns to retrieve
    cols = ["alarmname", "controllername", "message", "severity", "state"]

    # Fetch alarm data
    df: pd.DataFrame = integration.fetchDataV2(
        plant_name,
        "Alarm",
        ["Plant Alarm"],
        None,
        start,
        end
    )
    logger.info(f"Raw alarm dataframe:\n{df}")

    if 'state' in df.columns:
        # Filter to inactive alarms and select relevant columns
        df_filtered = df[df["state"] == "Inactive"][cols]
    
        return df_filtered.to_dict(orient="records")
    
    return "THERE IS NO ISSUE FOUND"




@mcp.tool(
    name="get_alarm_solar",
    description=(
        "Retrieves all inactive alarms for a specified solar plant within a given date range. "
        "Returns details including alarm name, controller name, message, severity, and state. "
        "This function aids in diagnosing solar plant issues such as breakdowns and maintenance requirements."
    )
)
def get_alarm_solar(
    plant_name: str,
    start_date: str,
    end_date: str
) -> List[Dict[str, str]]:
    """
    Fetches inactive alarm records for a specified solar plant between two dates,
    focusing on key details to assist in identifying and resolving plant issues.

    Args:
        plant_name (str): The name of the solar plant. Must start with the prefix defined in PLANT_PREFIX.
        start_date (str): Start date of the query range in 'YYYY-MM-DD' format.
        end_date (str): End date of the query range in 'YYYY-MM-DD' format.

    Returns:
        List[Dict[str, str]]: A list of dictionaries, each containing:
            - name: Alarm name (alarmname)
            - controller: Controller name (controllername)
            - message: Description of the alarm
            - severity: Severity level of the alarm
            - state: State of the alarm (expected to be "Inactive")

    Raises:
        ValueError: If the plant_name does not start with the required prefix,
                    or if start_date is after end_date,
                    or if date formats are incorrect.

    Example:
        get_alarm_solar("PLANT_SOLAR_XYZ", "2025-05-01", "2025-05-31")
    """
    logger.info(f"Called get_alarm_solar: {plant_name}, {start_date} to {end_date}")

    # Validate plant name
    if not plant_name.startswith(PLANT_PREFIX):
        raise ValueError(f"plant_name must start with '{PLANT_PREFIX}'")

    # Validate & parse dates
    start = _validate_date_str(start_date)
    end = _validate_date_str(end_date)
    if start > end:
        raise ValueError("start_date must be on or before end_date")

    # Define columns to retrieve
    cols = ["alarmname", "controllername", "message", "severity", "state"]

    # Fetch alarm data
    df: pd.DataFrame = integration.fetchDataV2(
        plant_name,
        "Alarm",
        ["Inverter Alarm"],
        None,
        start,
        end
    )
    logger.info(f"Raw alarm dataframe:\n{df}")


    if 'state' in df.columns:
        # Filter to inactive alarms and select relevant columns
        df_filtered = df[df["state"] == "Inactive"][cols]

        return df_filtered.to_dict(orient="records")
    
    return "THERE IS NO ISSUE FOUND"


OPENWEATHER_API_KEY = os.getenv("OPENWEATHER_API_KEY")
import requests
                

def fetch_weather_data(city: str, start_date: str, end_date: str):
    """
    Fetch weather data for a given city and date range.
    
    Args:
        city (str): City name
        start_date (str): Start date in YYYY-MM-DD format
        end_date (str): End date in YYYY-MM-DD format
    
    Returns:
        dict: Weather data for each day in the range
    """
    if not OPENWEATHER_API_KEY:
        return {"error": "Missing OpenWeather API key"}

    try:
        # Convert input dates
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
    except ValueError:
        return {"error": "Invalid date format. Use YYYY-MM-DD."}

    if start_dt > end_dt:
        return {"error": "Start date must be before end date."}

    # Get latitude/longitude of the city first (Geocoding API)
    geo_url = f"http://api.openweathermap.org/geo/1.0/direct?q={city}&limit=1&appid={OPENWEATHER_API_KEY}"
    geo_response = requests.get(geo_url)
    if geo_response.status_code != 200 or not geo_response.json():
        return {"error": f"Unable to fetch coordinates for {city}"}

    lat = geo_response.json()[0]["lat"]
    lon = geo_response.json()[0]["lon"]

    results = []
    current_date = start_dt

    while current_date <= end_dt:
        timestamp = int(current_date.timestamp())
        weather_url = (
            f"https://api.openweathermap.org/data/2.5/onecall/timemachine"
            f"?lat={lat}&lon={lon}&dt={timestamp}&appid={OPENWEATHER_API_KEY}"
        )

        response = requests.get(weather_url)
        if response.status_code == 200:
            data = response.json()
            results.append({
                "date": current_date.strftime("%Y-%m-%d"),
                "city": city,
                "temp": data.get("current", {}).get("temp"),
                "humidity": data.get("current", {}).get("humidity"),
                "weather": data.get("current", {}).get("weather", [{}])[0].get("description"),
            })
        else:
            results.append({
                "date": current_date.strftime("%Y-%m-%d"),
                "city": city,
                "error": "Weather data not available"
            })

        current_date += timedelta(days=1)

    return {"weather_history": results}


@mcp.tool(
    description=(
        "Fetches historical weather data (temperature, humidity, and weather description) for a specified city and date range "
        "using the OpenWeather API. Returns a JSON object containing daily weather details for each date in the range. "
        "If data is unavailable for a date or city, an error message is included for that entry. "
        "Input dates must be in YYYY-MM-DD format. "
        "Useful for retrieving past weather conditions to support energy analysis, reporting, or troubleshooting."
    )
)
def get_weather(city: str, start_date: str, end_date: str) -> str:
    """
    Fetch historical weather data for a given city and date range.
    
    Args:
        city (str): City name
        start_date (str): Start date (YYYY-MM-DD)
        end_date (str): End date (YYYY-MM-DD)
    """
    result = fetch_weather_data(city, start_date, end_date)
    return json.dumps(result, indent=2)

# =========================
# Main Entry Point
# =========================

if __name__ == "__main__":
    logger.info("Solar MCP server starting")
    try:
        # Default transport will be stdio. In production you might use grpc/http when supported.
        mcp.run(transport="stdio")
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    except Exception as exc:
        logger.exception("Unhandled exception while running MCP: %s", exc)
